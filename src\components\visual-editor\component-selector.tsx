"use client";

import { useState } from "react";
import { Search, Component, Blocks, Layout } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import type { EditableComponent } from "./types";

interface ComponentSelectorProps {
  components: EditableComponent[];
  selectedComponent: EditableComponent | null;
  onSelectComponent: (component: EditableComponent) => void;
}

export function ComponentSelector({
  components,
  selectedComponent,
  onSelectComponent,
}: ComponentSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Filter components based on search and tab
  const filteredComponents = components.filter((component) => {
    const matchesSearch = component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         component.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         component.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = activeTab === "all" || component.type === activeTab;
    
    return matchesSearch && matchesTab;
  });

  // Group components by type
  const componentsByType = {
    ui: filteredComponents.filter(c => c.type === 'ui'),
    component: filteredComponents.filter(c => c.type === 'component'),
    block: filteredComponents.filter(c => c.type === 'block'),
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'ui':
        return <Component className="size-4" />;
      case 'component':
        return <Blocks className="size-4" />;
      case 'block':
        return <Layout className="size-4" />;
      default:
        return <Component className="size-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'ui':
        return 'UI Primitives';
      case 'component':
        return 'Components';
      case 'block':
        return 'Blocks';
      default:
        return type;
    }
  };

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'ui':
        return 'secondary';
      case 'component':
        return 'default';
      case 'block':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h2 className="font-semibold text-lg mb-2">Components</h2>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          <Input
            placeholder="Search components..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
          <TabsTrigger value="ui" className="text-xs">UI</TabsTrigger>
          <TabsTrigger value="component" className="text-xs">Comp</TabsTrigger>
          <TabsTrigger value="block" className="text-xs">Block</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-4">
          <ScrollArea className="h-[calc(100vh-300px)]">
            <div className="space-y-6">
              {Object.entries(componentsByType).map(([type, typeComponents]) => (
                typeComponents.length > 0 && (
                  <div key={type}>
                    <div className="flex items-center gap-2 mb-3">
                      {getTypeIcon(type)}
                      <h3 className="font-medium text-sm">{getTypeLabel(type)}</h3>
                      <Badge variant="secondary" className="text-xs">
                        {typeComponents.length}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      {typeComponents.map((component) => (
                        <ComponentCard
                          key={component.id}
                          component={component}
                          isSelected={selectedComponent?.id === component.id}
                          onSelect={() => onSelectComponent(component)}
                        />
                      ))}
                    </div>
                  </div>
                )
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        {(['ui', 'component', 'block'] as const).map((type) => (
          <TabsContent key={type} value={type} className="mt-4">
            <ScrollArea className="h-[calc(100vh-300px)]">
              <div className="space-y-2">
                {componentsByType[type].map((component) => (
                  <ComponentCard
                    key={component.id}
                    component={component}
                    isSelected={selectedComponent?.id === component.id}
                    onSelect={() => onSelectComponent(component)}
                  />
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

interface ComponentCardProps {
  component: EditableComponent;
  isSelected: boolean;
  onSelect: () => void;
}

function ComponentCard({ component, isSelected, onSelect }: ComponentCardProps) {
  return (
    <Button
      variant={isSelected ? "default" : "ghost"}
      className={cn(
        "w-full justify-start h-auto p-3 text-left",
        isSelected && "bg-primary text-primary-foreground"
      )}
      onClick={onSelect}
    >
      <div className="space-y-1 w-full">
        <div className="flex items-center justify-between">
          <span className="font-medium text-sm">{component.title}</span>
          <Badge 
            variant={isSelected ? "secondary" : "outline"} 
            className="text-xs"
          >
            {component.type}
          </Badge>
        </div>
        <p className={cn(
          "text-xs line-clamp-2",
          isSelected ? "text-primary-foreground/80" : "text-muted-foreground"
        )}>
          {component.description}
        </p>
      </div>
    </Button>
  );
}
