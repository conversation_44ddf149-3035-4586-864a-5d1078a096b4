{"$schema": "https://biomejs.dev/schemas/1.8.2/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["node_modules", "playwright-report", ".next", ".vercel", ".idea", "public/r", "components/ui"]}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": "warn"}}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "javascript": {"formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "semicolons": "always", "trailingCommas": "all"}}}