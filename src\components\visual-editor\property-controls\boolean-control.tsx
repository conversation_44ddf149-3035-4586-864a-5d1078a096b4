"use client";

import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import type { PropertyControlProps } from "../types";

export function BooleanControl({ property, value, onChange }: PropertyControlProps) {
  const boolValue = Boolean(value);

  return (
    <div className="flex items-center justify-between">
      <Label htmlFor={property.name} className="text-sm font-medium">
        {property.label}
      </Label>
      <Switch
        id={property.name}
        checked={boolValue}
        onCheckedChange={onChange}
      />
    </div>
  );
}
