"use client";

import { useState, useEffect, cloneElement, isValidElement } from "react";
import { VisualEditingOverlay } from "./visual-editing-overlay";
import { componentConfigs } from "./component-configs";
import type { EditableComponentConfig } from "./types";

interface EditableComponentWrapperProps {
  componentName: string;
  children?: React.ReactNode;
  initialProps?: Record<string, any>;
}

export function EditableComponentWrapper({
  componentName,
  children,
  initialProps = {},
}: EditableComponentWrapperProps) {
  const config = componentConfigs[componentName];
  const [componentProps, setComponentProps] = useState(
    initialProps || config?.defaultProps || {}
  );

  useEffect(() => {
    if (config) {
      setComponentProps(prev => ({
        ...config.defaultProps,
        ...initialProps,
        ...prev,
      }));
    }
  }, [config, initialProps]);

  const handlePropsChange = (newProps: Record<string, any>) => {
    setComponentProps(newProps);
  };

  // If no config exists, render children as-is
  if (!config) {
    return <>{children}</>;
  }

  // Try to clone the element with new props if it's a valid React element
  const enhancedChildren = isValidElement(children)
    ? cloneElement(children, { ...componentProps })
    : children;

  return (
    <VisualEditingOverlay
      componentName={componentName}
      componentConfig={config}
      onPropsChange={handlePropsChange}
    >
      {enhancedChildren}
    </VisualEditingOverlay>
  );
}

// Hook for components that need to be editable
export function useEditableProps(componentName: string, defaultProps: Record<string, any> = {}) {
  const config = componentConfigs[componentName];
  const [props, setProps] = useState(defaultProps);

  useEffect(() => {
    if (config) {
      setProps(prev => ({
        ...config.defaultProps,
        ...defaultProps,
        ...prev,
      }));
    }
  }, [config, defaultProps]);

  return {
    props,
    setProps,
    isEditable: !!config,
    config,
  };
}
