"use client";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { PropertyControlProps } from "../types";

export function TextControl({ property, value, onChange }: PropertyControlProps) {
  const isMultiline = property.name.includes('description') || 
                     property.name.includes('content') ||
                     (typeof value === 'string' && value.length > 50);

  if (isMultiline) {
    return (
      <Textarea
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={`Enter ${property.label.toLowerCase()}...`}
        className="min-h-[80px] resize-none"
      />
    );
  }

  return (
    <Input
      type="text"
      value={value || ''}
      onChange={(e) => onChange(e.target.value)}
      placeholder={`Enter ${property.label.toLowerCase()}...`}
    />
  );
}
