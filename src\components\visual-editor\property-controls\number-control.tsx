"use client";

import { Input } from "@/components/ui/input";
import { <PERSON>lider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import type { PropertyControlProps } from "../types";

export function NumberControl({ property, value, onChange }: PropertyControlProps) {
  const numValue = typeof value === 'number' ? value : parseFloat(value) || 0;
  const min = property.min ?? 0;
  const max = property.max ?? 100;
  const step = property.step ?? 1;

  const handleSliderChange = (values: number[]) => {
    onChange(values[0]);
  };

  const handleInputChange = (inputValue: string) => {
    const num = parseFloat(inputValue);
    if (!isNaN(num)) {
      onChange(Math.min(Math.max(num, min), max));
    }
  };

  const increment = () => {
    const newValue = Math.min(numValue + step, max);
    onChange(newValue);
  };

  const decrement = () => {
    const newValue = Math.max(numValue - step, min);
    onChange(newValue);
  };

  // Show slider for ranges, input for specific values
  const showSlider = (max - min) <= 200 && step >= 1;

  return (
    <div className="space-y-3">
      {showSlider && (
        <div className="px-2">
          <Slider
            value={[numValue]}
            onValueChange={handleSliderChange}
            min={min}
            max={max}
            step={step}
            className="w-full"
          />
        </div>
      )}
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 flex-shrink-0"
          onClick={decrement}
          disabled={numValue <= min}
        >
          <Minus className="h-3 w-3" />
        </Button>
        
        <Input
          type="number"
          value={numValue}
          onChange={(e) => handleInputChange(e.target.value)}
          min={min}
          max={max}
          step={step}
          className="text-center font-mono"
        />
        
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8 flex-shrink-0"
          onClick={increment}
          disabled={numValue >= max}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
      
      {(min !== undefined || max !== undefined) && (
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{min}</span>
          <span>{max}</span>
        </div>
      )}
    </div>
  );
}
