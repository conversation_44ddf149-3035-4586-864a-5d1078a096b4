"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./dashboard-button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { roles } from "@/lib/dashboard-data"
import { ExternalLink } from "lucide-react"

export function DashboardSettingsGeneralPage() {
  return (
    <div className="space-y-10">
      {/* Personal Information */}
      <section aria-labelledby="personal-information">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2 
              id="personal-information"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Personal information
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Manage your personal information and role.
            </p>
          </div>
          <div className="md:col-span-2">
            <Card>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-6">
                  <div className="col-span-full sm:col-span-3">
                    <Label htmlFor="first-name" className="font-medium">
                      First name
                    </Label>
                    <Input
                      type="text"
                      id="first-name"
                      name="first-name"
                      placeholder="Emma"
                      className="mt-2"
                    />
                  </div>
                  <div className="col-span-full sm:col-span-3">
                    <Label htmlFor="last-name" className="font-medium">
                      Last name
                    </Label>
                    <Input
                      type="text"
                      id="last-name"  
                      name="last-name"
                      placeholder="Stone"
                      className="mt-2"
                    />
                  </div>
                  <div className="col-span-full">
                    <Label htmlFor="email" className="font-medium">
                      Email
                    </Label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      placeholder="<EMAIL>"
                      className="mt-2"
                    />
                  </div>
                  <div className="col-span-full sm:col-span-3">
                    <Label htmlFor="year" className="font-medium">
                      Birth year
                    </Label>
                    <Input
                      id="birthyear"
                      name="year"
                      type="number"
                      placeholder="1994"
                      className="mt-2"
                      min="1900"
                      max={new Date().getFullYear()}
                    />
                  </div>
                  <div className="col-span-full sm:col-span-3">
                    <Label htmlFor="role" className="font-medium">
                      Role
                    </Label>
                    <Select defaultValue="member" disabled>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="mt-2 text-xs text-gray-500">
                      Roles can only be changed by system admin.
                    </p>
                  </div>
                  <div className="col-span-full mt-6 flex justify-end">
                    <DashboardButton type="submit">
                      Save settings
                    </DashboardButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Separator />

      {/* Notification Settings */}
      <section aria-labelledby="notification-settings">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2
              id="notification-settings"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Notification settings
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Configure the types of notifications you want to receive.
            </p>
          </div>
          <div className="md:col-span-2">
            <Card>
              <CardContent className="p-6 space-y-6">
                <fieldset>
                  <legend className="text-sm font-medium text-gray-900 dark:text-gray-50">
                    Team
                  </legend>
                  <p className="mt-1 text-sm leading-6 text-gray-500">
                    Configure the types of team alerts you want to receive.
                  </p>
                  <div className="mt-4 space-y-4">
                    <div className="flex items-center space-x-3">
                      <Checkbox id="team-requests" defaultChecked />
                      <Label htmlFor="team-requests">Team join requests</Label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Checkbox id="team-activity-digest" />
                      <Label htmlFor="team-activity-digest">
                        Weekly team activity digest
                      </Label>
                    </div>
                  </div>
                </fieldset>
                
                <fieldset>
                  <legend className="text-sm font-medium text-gray-900 dark:text-gray-50">
                    Usage
                  </legend>
                  <p className="mt-1 text-sm leading-6 text-gray-500">
                    Configure the types of usage alerts you want to receive.
                  </p>
                  <div className="mt-4 space-y-4">
                    <div className="flex items-center space-x-3">
                      <Checkbox id="api-requests" />
                      <Label htmlFor="api-requests">API incidents</Label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Checkbox id="workspace-execution" />
                      <Label htmlFor="workspace-execution">
                        Platform incidents
                      </Label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Checkbox id="query-caching" defaultChecked />
                      <Label htmlFor="query-caching">
                        Payment transactions
                      </Label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Checkbox id="storage" defaultChecked />
                      <Label htmlFor="storage">User behavior</Label>
                    </div>
                  </div>
                </fieldset>
                
                <div className="flex justify-end">
                  <DashboardButton type="submit">
                    Save settings
                  </DashboardButton>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Separator />

      {/* Danger Zone */}
      <section aria-labelledby="danger-zone">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2
              id="danger-zone"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Danger zone
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Manage general workspace. Contact system admin for more information.{" "}
              <a
                href="#"
                className="inline-flex items-center gap-1 text-indigo-600 hover:underline hover:underline-offset-4 dark:text-indigo-400"
              >
                Learn more
                <ExternalLink className="size-4 shrink-0" />
              </a>
            </p>
          </div>
          <div className="space-y-6 md:col-span-2">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start justify-between gap-10">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50">
                      Leave workspace
                    </h4>
                    <p className="mt-2 text-sm leading-6 text-gray-500">
                      Revoke your access to this team. Other people you have
                      added to the workspace will remain.
                    </p>
                  </div>
                  <DashboardButton
                    variant="secondary"
                    className="text-red-600 dark:text-red-500"
                  >
                    Leave
                  </DashboardButton>
                </div>
              </CardContent>
            </Card>
            
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="flex items-start justify-between gap-10 p-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 dark:text-gray-600">
                      Delete workspace
                    </h4>
                    <p className="mt-2 text-sm leading-6 text-gray-400 dark:text-gray-600">
                      Permanently delete this workspace and all associated data.
                      This action cannot be undone.
                    </p>
                  </div>
                  <DashboardButton
                    variant="secondary"
                    disabled
                    className="whitespace-nowrap text-red-600 disabled:text-red-300 disabled:opacity-50 dark:text-red-500 disabled:dark:text-red-700"
                  >
                    Delete workspace
                  </DashboardButton>
                </div>
                <div className="border-t border-gray-200 bg-gray-50 p-4 dark:border-gray-900 dark:bg-gray-900">
                  <p className="text-sm text-gray-500">
                    You cannot delete the workspace because you are not the
                    system admin.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
