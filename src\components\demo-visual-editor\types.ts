export interface ComponentProperty {
  name: string;
  type: 'text' | 'color' | 'number' | 'select' | 'boolean';
  value: any;
  options?: string[] | number[];
  min?: number;
  max?: number;
  step?: number;
  label: string;
  description?: string;
}

export interface EditableComponentConfig {
  id: string;
  name: string;
  properties: ComponentProperty[];
  defaultProps: Record<string, any>;
}

export interface VisualEditingState {
  isEditing: boolean;
  selectedComponent: string | null;
  componentProps: Record<string, any>;
  showPropertyPanel: boolean;
  showCodeExport: boolean;
}
