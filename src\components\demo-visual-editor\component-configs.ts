import type { EditableComponentConfig } from "./types";

export const componentConfigs: Record<string, EditableComponentConfig> = {
  // Hero Component
  hero: {
    id: "hero",
    name: "Hero",
    properties: [
      {
        name: "title",
        type: "text",
        value: "Build a Registry",
        label: "Title",
        description: "Main heading text",
      },
      {
        name: "description",
        type: "text",
        value: "This starter helps you create a registry so you can distribute your custom components, hooks, pages, and other files to any React project",
        label: "Description",
        description: "Supporting description text",
      },
      {
        name: "buttonText",
        type: "text",
        value: "Learn more",
        label: "Button Text",
        description: "Call-to-action button text",
      },
      {
        name: "buttonLink",
        type: "text",
        value: "#sale",
        label: "Button Link",
        description: "URL for the button",
      },
    ],
    defaultProps: {
      title: "Build a Registry",
      description: "This starter helps you create a registry so you can distribute your custom components, hooks, pages, and other files to any React project",
      buttonText: "Learn more",
      buttonLink: "#sale",
    },
  },

  // Button Component
  button: {
    id: "button",
    name: "<PERSON><PERSON>",
    properties: [
      {
        name: "children",
        type: "text",
        value: "Click me",
        label: "Text",
        description: "Button text content",
      },
      {
        name: "variant",
        type: "select",
        value: "default",
        options: ["default", "destructive", "outline", "secondary", "ghost", "link"],
        label: "Variant",
        description: "Button style variant",
      },
      {
        name: "size",
        type: "select",
        value: "default",
        options: ["default", "sm", "lg", "icon"],
        label: "Size",
        description: "Button size",
      },
      {
        name: "disabled",
        type: "boolean",
        value: false,
        label: "Disabled",
        description: "Whether the button is disabled",
      },
    ],
    defaultProps: {
      children: "Click me",
      variant: "default",
      size: "default",
      disabled: false,
    },
  },

  // Badge Component
  badge: {
    id: "badge",
    name: "Badge",
    properties: [
      {
        name: "children",
        type: "text",
        value: "Badge",
        label: "Text",
        description: "Badge text content",
      },
      {
        name: "variant",
        type: "select",
        value: "default",
        options: ["default", "secondary", "destructive", "outline"],
        label: "Variant",
        description: "Badge style variant",
      },
    ],
    defaultProps: {
      children: "Badge",
      variant: "default",
    },
  },

  // Input Component
  input: {
    id: "input",
    name: "Input",
    properties: [
      {
        name: "placeholder",
        type: "text",
        value: "Enter text...",
        label: "Placeholder",
        description: "Placeholder text",
      },
      {
        name: "type",
        type: "select",
        value: "text",
        options: ["text", "email", "password", "number", "tel", "url"],
        label: "Type",
        description: "Input type",
      },
      {
        name: "disabled",
        type: "boolean",
        value: false,
        label: "Disabled",
        description: "Whether the input is disabled",
      },
    ],
    defaultProps: {
      placeholder: "Enter text...",
      type: "text",
      disabled: false,
    },
  },

  // Card Component
  card: {
    id: "card",
    name: "Card",
    properties: [
      {
        name: "title",
        type: "text",
        value: "Card Title",
        label: "Title",
        description: "Card title text",
      },
      {
        name: "description",
        type: "text",
        value: "Card description goes here",
        label: "Description",
        description: "Card description text",
      },
    ],
    defaultProps: {
      title: "Card Title",
      description: "Card description goes here",
    },
  },

  // Alert Component
  alert: {
    id: "alert",
    name: "Alert",
    properties: [
      {
        name: "title",
        type: "text",
        value: "Heads up!",
        label: "Title",
        description: "Alert title",
      },
      {
        name: "description",
        type: "text",
        value: "You can add components to your app using the cli.",
        label: "Description",
        description: "Alert description",
      },
      {
        name: "variant",
        type: "select",
        value: "default",
        options: ["default", "destructive"],
        label: "Variant",
        description: "Alert variant",
      },
    ],
    defaultProps: {
      title: "Heads up!",
      description: "You can add components to your app using the cli.",
      variant: "default",
    },
  },
};
