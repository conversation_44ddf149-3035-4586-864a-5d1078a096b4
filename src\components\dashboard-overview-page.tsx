"use client"

import { DashboardChartCard, getBadgeType } from "./dashboard-chart-card"
import { DashboardButton } from "./dashboard-button"
import { DashboardBadge } from "./dashboard-badge"
import { sampleOverviewData } from "@/lib/dashboard-schema"
import { cx, formatters, percentageFormatter } from "@/lib/dashboard-utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export type KpiEntry = {
  title: string
  percentage: number
  current: number
  allowed: number
  unit?: string
}

const usageData: KpiEntry[] = [
  {
    title: "Rows read",
    percentage: 48.1,
    current: 48.1,
    allowed: 100,
    unit: "M",
  },
  {
    title: "Rows written", 
    percentage: 78.3,
    current: 78.3,
    allowed: 100,
    unit: "M",
  },
  {
    title: "Storage",
    percentage: 26,
    current: 5.2,
    allowed: 20,
    unit: "GB",
  },
]

const workspaceData: KpiEntry[] = [
  {
    title: "Weekly active users",
    percentage: 21.7,
    current: 21.7,
    allowed: 100,
    unit: "%",
  },
  {
    title: "Total users",
    percentage: 70,
    current: 28,
    allowed: 40,
  },
  {
    title: "Uptime",
    percentage: 98.3,
    current: 98.3,
    allowed: 100,
    unit: "%",
  },
]

export type KpiEntryExtended = Omit<KpiEntry, "current" | "allowed" | "unit"> & {
  value: string
  color: string
}

const costsData: KpiEntryExtended[] = [
  {
    title: "Base tier",
    percentage: 68.1,
    value: "$200",
    color: "bg-indigo-600 dark:bg-indigo-500",
  },
  {
    title: "On-demand charges",
    percentage: 20.8,
    value: "$61.1",
    color: "bg-purple-600 dark:bg-purple-500",
  },
  {
    title: "Caching",
    percentage: 11.1,
    value: "$31.9",
    color: "bg-gray-400 dark:bg-gray-600",
  },
]

function ProgressBarCard({ 
  title, 
  change, 
  value, 
  valueDescription, 
  ctaDescription, 
  ctaText, 
  data 
}: {
  title: string
  change: string
  value: string
  valueDescription: string
  ctaDescription: string
  ctaText: string
  data: KpiEntry[]
}) {
  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-50">
            {title}
          </CardTitle>
          <DashboardBadge variant={change.startsWith('+') ? 'success' : 'error'}>
            {change}
          </DashboardBadge>
        </div>
        <div className="mt-2">
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-50">
            {value}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {valueDescription}
          </p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {data.map((item) => (
            <div key={item.title} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900 dark:text-gray-50">
                    {item.title}
                  </span>
                  <span className="text-gray-500">
                    {item.current}{item.unit} / {item.allowed}{item.unit}
                  </span>
                </div>
                <div className="mt-1 h-2 bg-gray-200 rounded-full dark:bg-gray-800">
                  <div 
                    className="h-2 bg-indigo-600 rounded-full transition-all duration-300 dark:bg-indigo-500"
                    style={{ width: `${item.percentage}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {ctaDescription}{" "}
            <button className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
              {ctaText}
            </button>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

function CategoryBarCard({
  title,
  change,
  value, 
  valueDescription,
  subtitle,
  ctaDescription,
  ctaText,
  data
}: {
  title: string
  change: string
  value: string
  valueDescription: string
  subtitle: string
  ctaDescription: string
  ctaText: string  
  data: KpiEntryExtended[]
}) {
  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-50">
            {title}
          </CardTitle>
          <DashboardBadge variant={change.startsWith('+') ? 'success' : 'error'}>
            {change}
          </DashboardBadge>
        </div>
        <div className="mt-2">
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-50">
            {value}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {valueDescription}
          </p>
        </div>
      </CardHeader>
      <CardContent>
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-50 mb-3">
          {subtitle}
        </h4>
        <div className="space-y-3">
          {data.map((item) => (
            <div key={item.title} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={cx("w-3 h-3 rounded-full", item.color)} />
                <span className="text-sm text-gray-900 dark:text-gray-50">
                  {item.title}
                </span>
              </div>
              <div className="text-sm font-medium text-gray-900 dark:text-gray-50">
                {item.value}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {ctaDescription}{" "}
            <button className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
              {ctaText}
            </button>
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export function DashboardOverviewPage() {
  const currentData = sampleOverviewData[sampleOverviewData.length - 1]
  const previousData = sampleOverviewData[sampleOverviewData.length - 2]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-50 sm:text-xl">
          Current billing cycle
        </h1>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <ProgressBarCard
          title="Usage"
          change="+0.2%"
          value="68.1%"
          valueDescription="of allowed capacity"
          ctaDescription="Monthly usage resets in 12 days."
          ctaText="Manage plan."
          data={usageData}
        />
        <ProgressBarCard
          title="Workspace"
          change="+2.9%"
          value="21.7%"
          valueDescription="weekly active users"
          ctaDescription="Add up to 20 members in free plan."
          ctaText="Invite users."
          data={workspaceData}
        />
        <CategoryBarCard
          title="Costs"
          change="-1.4%"
          value="$293.5"
          valueDescription="current billing cycle"
          subtitle="Current costs"
          ctaDescription="Set hard caps in"
          ctaText="cost spend management." 
          data={costsData}
        />
      </div>

      {/* Overview Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-50 sm:text-xl">
            Overview
          </h2>
          <div className="flex items-center gap-2">
            <DashboardButton variant="secondary">
              Export Data
            </DashboardButton>
          </div>
        </div>

        {/* Chart Cards Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <DashboardChartCard
            title="Rows Written"
            value={currentData["Rows written"]}
            previousValue={previousData["Rows written"]}
            type="unit"
          />
          <DashboardChartCard
            title="Rows Read"
            value={currentData["Rows read"]}
            previousValue={previousData["Rows read"]}
            type="unit"
          />
          <DashboardChartCard
            title="Queries"
            value={currentData["Queries"]}
            previousValue={previousData["Queries"]}
            type="unit"
          />
          <DashboardChartCard
            title="Payments Completed"
            value={currentData["Payments completed"]}
            previousValue={previousData["Payments completed"]}
            type="unit"
          />
          <DashboardChartCard
            title="Sign Ups"
            value={currentData["Sign ups"]}
            previousValue={previousData["Sign ups"]}
            type="unit"
          />
          <DashboardChartCard
            title="Support Calls"
            value={currentData["Support calls"]}
            previousValue={previousData["Support calls"]}
            type="unit"
          />
        </div>
      </div>
    </div>
  )
}
