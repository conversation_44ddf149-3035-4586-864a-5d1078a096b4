"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  Sun, 
  Moon, 
  Code, 
  RotateCcw, 
  Download,
  Eye,
  EyeOff
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { EditableComponent } from "./types";

interface ToolbarProps {
  previewMode: 'desktop' | 'tablet' | 'mobile';
  isDarkMode: boolean;
  isCodeViewOpen: boolean;
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void;
  onToggleDarkMode: () => void;
  onToggleCodeView: () => void;
  onReset: () => void;
  onExport: () => void;
  selectedComponent: EditableComponent | null;
}

export function Toolbar({
  previewMode,
  isDarkMode,
  isCodeViewOpen,
  onPreviewModeChange,
  onToggleDarkMode,
  onToggleCodeView,
  onReset,
  onExport,
  selectedComponent,
}: ToolbarProps) {
  const previewModes = [
    { key: 'desktop' as const, icon: Monitor, label: 'Desktop' },
    { key: 'tablet' as const, icon: Tablet, label: 'Tablet' },
    { key: 'mobile' as const, icon: Smartphone, label: 'Mobile' },
  ];

  return (
    <div className="h-16 border-b bg-background px-4 flex items-center justify-between">
      {/* Left Section - Component Info */}
      <div className="flex items-center space-x-4">
        <div className="font-semibold text-lg">Visual Editor</div>
        {selectedComponent && (
          <>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <span className="font-medium">{selectedComponent.title}</span>
              <Badge variant="outline" className="text-xs">
                {selectedComponent.type}
              </Badge>
            </div>
          </>
        )}
      </div>

      {/* Center Section - Preview Controls */}
      <div className="flex items-center space-x-2">
        {/* Preview Mode Selector */}
        <div className="flex items-center bg-muted rounded-md p-1">
          {previewModes.map(({ key, icon: Icon, label }) => (
            <Button
              key={key}
              variant={previewMode === key ? "default" : "ghost"}
              size="sm"
              className={cn(
                "h-8 px-3",
                previewMode === key && "bg-background shadow-sm"
              )}
              onClick={() => onPreviewModeChange(key)}
              title={label}
            >
              <Icon className="h-4 w-4" />
            </Button>
          ))}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Dark Mode Toggle */}
        <Button
          variant={isDarkMode ? "default" : "outline"}
          size="sm"
          className="h-8 px-3"
          onClick={onToggleDarkMode}
          title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
        >
          {isDarkMode ? (
            <Moon className="h-4 w-4" />
          ) : (
            <Sun className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Right Section - Actions */}
      <div className="flex items-center space-x-2">
        {/* Code View Toggle */}
        <Button
          variant={isCodeViewOpen ? "default" : "outline"}
          size="sm"
          className="h-8 px-3"
          onClick={onToggleCodeView}
          title={isCodeViewOpen ? "Hide Code" : "Show Code"}
        >
          {isCodeViewOpen ? (
            <EyeOff className="h-4 w-4 mr-1" />
          ) : (
            <Eye className="h-4 w-4 mr-1" />
          )}
          Code
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Reset Button */}
        <Button
          variant="outline"
          size="sm"
          className="h-8 px-3"
          onClick={onReset}
          disabled={!selectedComponent}
          title="Reset to Default"
        >
          <RotateCcw className="h-4 w-4 mr-1" />
          Reset
        </Button>

        {/* Export Button */}
        <Button
          variant="default"
          size="sm"
          className="h-8 px-3"
          onClick={onExport}
          disabled={!selectedComponent}
          title="Export Code"
        >
          <Download className="h-4 w-4 mr-1" />
          Export
        </Button>
      </div>
    </div>
  );
}
