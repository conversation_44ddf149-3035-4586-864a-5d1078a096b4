"use client"

import { useState } from "react"
import { useParams } from "next/navigation"
import { getRegistryItem } from "@/lib/registry"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { OpenInV0Button } from "@/components/registry/open-in-v0"
import { VisualEditorWithPreview } from "@/components/visual-editor-with-preview"
import { Eye, Code2, Palette, Settings, ExternalLink, ArrowLeft } from "lucide-react"
import Link from "next/link"

// Import demo components for preview
import { CompleteDashboard } from "@/components/complete-dashboard"
import { DashboardButton } from "@/components/dashboard-button"
import { MultiPageDashboard } from "@/components/multi-page-dashboard"

const DemoComponents: Record<string, React.ComponentType<any>> = {
  "complete-dashboard": CompleteDashboard,
  "multi-page-dashboard": MultiPageDashboard,
  "dashboard-button": DashboardButton,
  // Add more as needed
}

function ComponentDemo({ componentName }: { componentName: string }) {
  const Component = DemoComponents[componentName]
  
  if (!Component) {
    return (
      <div className="flex h-64 w-full items-center justify-center rounded-lg bg-gray-50 dark:bg-gray-900">
        <p className="text-gray-500">Preview not available</p>
      </div>
    )
  }
  
  return (
    <div className="w-full">
      <Component />
    </div>
  )
}

export default function EnhancedComponentPage() {
  const params = useParams()
  const componentName = params.name as string
  const [activeTab, setActiveTab] = useState("preview")

  try {
    const component = getRegistryItem(componentName)
    const registryUrl = `${window.location.origin}/r/${componentName}.json`

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/" className="flex items-center text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100">
              <ArrowLeft className="w-4 h-4 mr-1" />
              Back to Registry
            </Link>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold">{component.title}</h1>
                <Badge variant="outline" className="text-xs">
                  {component.type.replace('registry:', '')}
                </Badge>
              </div>
              {component.description && (
                <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
                  {component.description}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" asChild>
                <Link href={`/demo/${componentName}`}>
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Full Demo
                </Link>
              </Button>
              <OpenInV0Button 
                registryUrl={registryUrl}
                title={component.title}
                prompt={`Customize this ${component.title.toLowerCase()} component`}
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Visual Editor
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center gap-2">
              <Code2 className="w-4 h-4" />
              Code
            </TabsTrigger>
          </TabsList>

          {/* Preview Tab */}
          <TabsContent value="preview" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Component Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <ComponentDemo componentName={componentName} />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Visual Editor Tab */}
          <TabsContent value="editor" className="mt-6">
            <VisualEditorWithPreview 
              componentName={componentName}
              initialProps={{}}
            />
          </TabsContent>

          {/* Code Tab */}
          <TabsContent value="code" className="mt-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code2 className="w-5 h-5" />
                    Installation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Install via shadcn CLI</h4>
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                        <code>{`npx shadcn@latest add ${registryUrl}`}</code>
                      </pre>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Manual Installation</h4>
                      <pre className="bg-gray-900 text-gray-100 p-4 rounded-md text-sm overflow-x-auto">
                        <code>{`// Copy the component code and paste it into your project
// Make sure to install the required dependencies`}</code>
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-md text-sm overflow-x-auto">
                    <code>{`import { ${component.title.replace(/\s+/g, '')} } from "@/components/${componentName}"

export default function Example() {
  return (
    <${component.title.replace(/\s+/g, '')} />
  )
}`}</code>
                  </pre>
                </CardContent>
              </Card>

              {component.files && component.files.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Files</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {component.files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                          <span className="font-mono text-sm">{file.path}</span>
                          <Badge variant="outline" className="text-xs">
                            {file.type}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Additional Information */}
        <div className="mt-12 grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Component Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                <Badge variant="outline">{component.type.replace('registry:', '')}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Name:</span>
                <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                  {component.name}
                </code>
              </div>
              {component.files && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Files:</span>
                  <span className="text-sm">{component.files.length}</span>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/demo/${componentName}`}>
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Full Demo
                </Link>
              </Button>
              
              <OpenInV0Button 
                registryUrl={registryUrl}
                title={component.title}
                prompt={`Customize this ${component.title.toLowerCase()} component`}
                className="w-full"
              />
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => {
                  navigator.clipboard.writeText(`npx shadcn@latest add ${registryUrl}`)
                  // Add toast notification here
                }}
              >
                <Code2 className="w-4 h-4 mr-2" />
                Copy Install Command
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Component Not Found
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              The component "{componentName}" could not be found in the registry.
            </p>
            <Button asChild>
              <Link href="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Registry
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }
}
