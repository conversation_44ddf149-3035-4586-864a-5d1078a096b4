"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Edit, Settings, Code, X, Eye, EyeOff } from "lucide-react";
import { PropertyPanel } from "./property-panel";
import { CodeExportPanel } from "./code-export-panel";
import { cn } from "@/lib/utils";
import type { EditableComponentConfig, VisualEditingState } from "./types";

interface VisualEditingOverlayProps {
  componentName: string;
  componentConfig?: EditableComponentConfig;
  children: React.ReactNode;
  onPropsChange?: (props: Record<string, any>) => void;
}

export function VisualEditingOverlay({
  componentName,
  componentConfig,
  children,
  onPropsChange,
}: VisualEditingOverlayProps) {
  const [state, setState] = useState<VisualEditingState>({
    isEditing: false,
    selectedComponent: null,
    componentProps: componentConfig?.defaultProps || {},
    showPropertyPanel: false,
    showCodeExport: false,
  });

  const [isHovered, setIsHovered] = useState(false);
  const [showEditButton, setShowEditButton] = useState(false);

  useEffect(() => {
    if (componentConfig) {
      setState(prev => ({
        ...prev,
        componentProps: { ...componentConfig.defaultProps },
      }));
    }
  }, [componentConfig]);

  const handleEditClick = () => {
    setState(prev => ({
      ...prev,
      isEditing: true,
      selectedComponent: componentName,
      showPropertyPanel: true,
    }));
  };

  const handlePropsChange = (propName: string, value: any) => {
    const newProps = {
      ...state.componentProps,
      [propName]: value,
    };
    
    setState(prev => ({
      ...prev,
      componentProps: newProps,
    }));

    onPropsChange?.(newProps);
  };

  const handleStopEditing = () => {
    setState(prev => ({
      ...prev,
      isEditing: false,
      selectedComponent: null,
      showPropertyPanel: false,
      showCodeExport: false,
    }));
  };

  if (!componentConfig) {
    return <>{children}</>;
  }

  return (
    <div
      className="relative group"
      onMouseEnter={() => {
        setIsHovered(true);
        setShowEditButton(true);
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        if (!state.isEditing) {
          setShowEditButton(false);
        }
      }}
    >
      {/* Component wrapper with visual editing indicators */}
      <div
        className={cn(
          "relative transition-all duration-200",
          isHovered && "ring-2 ring-primary/50 ring-offset-2",
          state.isEditing && "ring-2 ring-primary ring-offset-2"
        )}
      >
        {children}
        
        {/* Edit button overlay */}
        {(showEditButton || state.isEditing) && (
          <div className="absolute top-2 right-2 flex items-center gap-2 z-10">
            <Badge variant="secondary" className="text-xs">
              {componentConfig.name}
            </Badge>
            
            {!state.isEditing ? (
              <Button
                size="sm"
                variant="default"
                onClick={handleEditClick}
                className="h-8 px-2"
              >
                <Edit className="h-3 w-3 mr-1" />
                Edit
              </Button>
            ) : (
              <Button
                size="sm"
                variant="outline"
                onClick={handleStopEditing}
                className="h-8 px-2"
              >
                <X className="h-3 w-3 mr-1" />
                Done
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Visual editing panels */}
      {state.isEditing && (
        <>
          {/* Property Panel Sheet */}
          <Sheet open={state.showPropertyPanel} onOpenChange={(open) => 
            setState(prev => ({ ...prev, showPropertyPanel: open }))
          }>
            <SheetTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="fixed top-4 left-4 z-50"
              >
                <Settings className="h-4 w-4 mr-2" />
                Properties
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <SheetHeader>
                <SheetTitle>Edit {componentConfig.name}</SheetTitle>
              </SheetHeader>
              <div className="mt-6">
                <PropertyPanel
                  config={componentConfig}
                  values={state.componentProps}
                  onChange={handlePropsChange}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Code Export Sheet */}
          <Sheet open={state.showCodeExport} onOpenChange={(open) => 
            setState(prev => ({ ...prev, showCodeExport: open }))
          }>
            <SheetTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="fixed top-4 left-32 z-50"
              >
                <Code className="h-4 w-4 mr-2" />
                Export Code
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-96">
              <SheetHeader>
                <SheetTitle>Export Code</SheetTitle>
              </SheetHeader>
              <div className="mt-6">
                <CodeExportPanel
                  componentName={componentConfig.name}
                  props={state.componentProps}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Floating action buttons */}
          <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
            <div className="flex items-center gap-2 bg-background border rounded-lg p-2 shadow-lg">
              <Button
                size="sm"
                variant={state.showPropertyPanel ? "default" : "outline"}
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  showPropertyPanel: !prev.showPropertyPanel 
                }))}
              >
                <Settings className="h-4 w-4 mr-1" />
                Properties
              </Button>
              
              <Button
                size="sm"
                variant={state.showCodeExport ? "default" : "outline"}
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  showCodeExport: !prev.showCodeExport 
                }))}
              >
                <Code className="h-4 w-4 mr-1" />
                Code
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={handleStopEditing}
              >
                <X className="h-4 w-4 mr-1" />
                Done
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
