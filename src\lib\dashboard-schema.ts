// Sample data schema for dashboard
export type Usage = {
  owner: string
  status: string
  costs: number
  region: string
  stability: number
  lastEdited: string
}

export type OverviewData = {
  date: string
  "Rows written": number
  "Rows read": number
  Queries: number
  "Payments completed": number
  "Sign ups": number
  Logins: number
  "Sign outs": number
  "Support calls": number
}

// Sample data for demo
export const sampleOverviewData: OverviewData[] = [
  {
    date: "2025-01-20",
    "Rows written": 12450,
    "Rows read": 34200,
    Queries: 890,
    "Payments completed": 156,
    "Sign ups": 23,
    Logins: 342,
    "Sign outs": 89,
    "Support calls": 12,
  },
  {
    date: "2025-01-21",
    "Rows written": 13200,
    "Rows read": 36100,
    Queries: 945,
    "Payments completed": 167,
    "Sign ups": 28,
    Logins: 378,
    "Sign outs": 92,
    "Support calls": 8,
  },
  {
    date: "2025-01-22", 
    "Rows written": 11800,
    "Rows read": 32500,
    Queries: 823,
    "Payments completed": 142,
    "Sign ups": 19,
    Logins: 298,
    "Sign outs": 76,
    "Support calls": 15,
  },
  {
    date: "2025-01-23",
    "Rows written": 14500,
    "Rows read": 38900,
    Queries: 1020,
    "Payments completed": 189,
    "Sign ups": 31,
    Logins: 412,
    "Sign outs": 103,
    "Support calls": 7,
  },
  {
    date: "2025-01-24",
    "Rows written": 13800,
    "Rows read": 37200,
    Queries: 967,
    "Payments completed": 173,
    "Sign ups": 26,
    Logins: 389,
    "Sign outs": 95,
    "Support calls": 11,
  },
]
