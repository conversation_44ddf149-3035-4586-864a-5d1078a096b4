import { VisualEditorWithPreview } from "@/components/visual-editor-with-preview"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Palette, Eye, Code2 } from "lucide-react"

export default function VisualEditorDemo() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="container mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Palette className="w-8 h-8 text-indigo-600" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-50">
              Visual Component Editor
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Customize your dashboard components visually - no code required. 
            Change colors, text, and properties with an intuitive interface.
          </p>
        </div>

        {/* Features */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card>
            <CardHeader className="text-center">
              <Eye className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <CardTitle>Live Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                See your changes instantly with real-time preview as you customize components.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <Palette className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <CardTitle>Visual Controls</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                Use color pickers, dropdowns, and text inputs to customize without coding.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="text-center">
              <Code2 className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <CardTitle>Export Code</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 text-center">
                Generate clean, production-ready code that you can copy and use immediately.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Demo Section */}
        <div className="space-y-12">
          <section>
            <h2 className="text-2xl font-bold mb-6 text-center">Try the Visual Editor</h2>
            <p className="text-gray-600 dark:text-gray-400 text-center mb-8">
              Select a component below to start customizing it visually:
            </p>
            
            <div className="grid gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Dashboard Button Editor</CardTitle>
                </CardHeader>
                <CardContent>
                  <VisualEditorWithPreview 
                    componentName="dashboard-button"
                    initialProps={{
                      children: "Click me",
                      variant: "primary"
                    }}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Dashboard Card Editor</CardTitle>
                </CardHeader>
                <CardContent>
                  <VisualEditorWithPreview 
                    componentName="dashboard-card"
                    initialProps={{
                      title: "Sample Card",
                      description: "This is a sample dashboard card",
                      color: "indigo"
                    }}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Login Form Editor</CardTitle>
                </CardHeader>
                <CardContent>
                  <VisualEditorWithPreview 
                    componentName="login"
                    initialProps={{
                      title: "Welcome back",
                      subtitle: "Enter your email to sign in to your account",
                      brandName: "Your Brand",
                      primaryColor: "green"
                    }}
                  />
                </CardContent>
              </Card>
            </div>
          </section>
        </div>

        {/* Instructions */}
        <div className="mt-16 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950 dark:to-purple-950 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-center mb-6">How to Use the Visual Editor</h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-3">
                1
              </div>
              <h4 className="font-semibold mb-2">Choose Component</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Browse the registry and select any component to customize
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-3">
                2
              </div>
              <h4 className="font-semibold mb-2">Customize Visually</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Use the visual controls to change colors, text, and properties
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-3">
                3
              </div>
              <h4 className="font-semibold mb-2">Preview Changes</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                See your changes instantly in the live preview panel
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-3">
                4
              </div>
              <h4 className="font-semibold mb-2">Export Code</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Copy the generated code and use it in your project
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
