# Visual Editor

A comprehensive visual editing system for the Registry Starter that allows users to visually edit components, blocks, and dashboards with real-time preview and live code export.

## Features

### 🎨 Visual Component Editing
- **Component Selection**: Browse and select from UI primitives, components, and blocks
- **Property Controls**: Figma-style property panels with various input types
- **Real-time Preview**: See changes instantly as you edit
- **Component Variants**: Quick switching between predefined component variants

### 🎛️ Property Controls
- **Text Controls**: Single-line and multi-line text inputs
- **Color Controls**: Color picker with presets and hex input
- **Number Controls**: Sliders and numeric inputs with min/max constraints
- **Select Controls**: Dropdown menus for predefined options
- **Boolean Controls**: Toggle switches for on/off properties
- **Spacing Controls**: Advanced spacing editor with linked/unlinked values
- **Typography Controls**: Font family, size, weight, and style controls

### 🎨 Design Token Management
- **Live Token Editing**: Modify CSS custom properties in real-time
- **Token Categories**: Organized by color, spacing, typography, border, etc.
- **Component-Specific Tokens**: Highlight tokens relevant to selected component
- **Search and Filter**: Find tokens quickly with search and category filters

### 📱 Responsive Preview
- **Multiple Viewports**: Desktop, tablet, and mobile preview modes
- **Responsive Design**: Test how components look across different screen sizes
- **Dark Mode Toggle**: Switch between light and dark themes

### 💻 Live Code Export
- **Component JSX**: Export the edited component with all props
- **Import Statements**: Generate proper import statements
- **CSS Tokens**: Export modified design tokens
- **Full Page Code**: Complete page template with the component
- **Copy to Clipboard**: One-click copying of generated code
- **Download Files**: Download complete component files

## Usage

### Accessing the Visual Editor

Navigate to `/visual-editor` in your application to access the visual editor interface.

### Interface Layout

The visual editor uses a three-panel layout:

1. **Left Panel**: Component selection and property controls
2. **Center Panel**: Live preview with responsive controls
3. **Right Panel**: Design tokens and code export

### Editing Workflow

1. **Select a Component**: Choose from the component list in the left panel
2. **Edit Properties**: Use the property controls to modify component props
3. **Adjust Design Tokens**: Fine-tune colors, spacing, and other design tokens
4. **Preview Changes**: See real-time updates in the center preview panel
5. **Export Code**: Generate and copy the code for your edited component

### Adding New Components

To make a component editable in the visual editor:

1. **Define the Component**: Add it to `config/editable-components.ts`
2. **Specify Properties**: Define the editable properties with their types
3. **Map Design Tokens**: List the CSS variables the component uses
4. **Set Default Props**: Provide default values for all properties

Example:
```typescript
{
  id: "my-component",
  name: "MyComponent",
  type: "component",
  title: "My Custom Component",
  description: "A custom component for demonstration",
  component: MyComponent,
  properties: [
    {
      name: "title",
      type: "text",
      value: "Default Title",
      label: "Title",
      description: "Component title text",
    },
    // ... more properties
  ],
  designTokens: ["--primary", "--background"],
  defaultProps: {
    title: "Default Title",
  },
}
```

### Adding Design Tokens

Add new design tokens to `config/design-tokens.ts`:

```typescript
{
  name: "My Color",
  value: "#ff0000",
  type: "color",
  category: "color",
  cssVar: "--my-color",
}
```

## Architecture

### Core Components

- **VisualEditor**: Main editor container with layout management
- **ComponentSelector**: Component browsing and selection interface
- **PropertyPanel**: Dynamic property controls based on component definition
- **PreviewPanel**: Live component preview with responsive modes
- **DesignTokenPanel**: Design token management interface
- **CodeExportPanel**: Code generation and export functionality
- **Toolbar**: Top toolbar with preview controls and actions

### State Management

The editor uses a custom hook `useVisualEditor` for state management:

- Component selection and props
- Design token values
- Preview mode and theme
- Code export functionality

### Property Control System

The property control system is extensible and supports:

- Custom control types
- Validation and constraints
- Real-time updates
- Type-safe property definitions

## Integration

The visual editor integrates seamlessly with the existing registry system:

- Uses the same component definitions
- Respects existing design tokens
- Maintains compatibility with the demo system
- Exports code that works with the registry

## Future Enhancements

Potential future improvements:

- **Drag & Drop**: Visual component composition
- **Layout Editor**: Grid and flexbox layout controls
- **Animation Controls**: CSS animation and transition editing
- **Theme Generator**: Automatic theme generation from brand colors
- **Component Library**: Save and reuse custom component configurations
- **Collaboration**: Real-time collaborative editing
- **Version History**: Undo/redo and version management
