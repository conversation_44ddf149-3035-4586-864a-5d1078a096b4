"use client";

import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Search, ChevronDown, ChevronRight, Palette, Type, Move, Square } from "lucide-react";
import { cn } from "@/lib/utils";
import type { DesignToken, EditableComponent } from "./types";

interface DesignTokenPanelProps {
  tokens: DesignToken[];
  values: Record<string, string>;
  onChange: (tokenVar: string, value: string) => void;
  selectedComponent: EditableComponent | null;
}

export function DesignTokenPanel({
  tokens,
  values,
  onChange,
  selectedComponent,
}: DesignTokenPanelProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [openCategories, setOpenCategories] = useState<Set<string>>(
    new Set(['color', 'spacing', 'typography', 'border'])
  );

  const toggleCategory = (category: string) => {
    const newOpenCategories = new Set(openCategories);
    if (newOpenCategories.has(category)) {
      newOpenCategories.delete(category);
    } else {
      newOpenCategories.add(category);
    }
    setOpenCategories(newOpenCategories);
  };

  // Filter tokens based on search and tab
  const filteredTokens = tokens.filter((token) => {
    const matchesSearch = token.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         token.cssVar.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = activeTab === "all" || token.type === activeTab;
    
    // If a component is selected, prioritize tokens it uses
    const isRelevantToComponent = !selectedComponent || 
                                 selectedComponent.designTokens.includes(token.cssVar);
    
    return matchesSearch && matchesTab && (activeTab === "all" || isRelevantToComponent);
  });

  // Group tokens by category
  const tokensByCategory = filteredTokens.reduce((acc, token) => {
    if (!acc[token.category]) {
      acc[token.category] = [];
    }
    acc[token.category].push(token);
    return acc;
  }, {} as Record<string, DesignToken[]>);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'color':
        return <Palette className="size-4" />;
      case 'typography':
        return <Type className="size-4" />;
      case 'spacing':
        return <Move className="size-4" />;
      case 'border':
        return <Square className="size-4" />;
      default:
        return <div className="size-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b space-y-4">
        <div>
          <h3 className="font-semibold text-lg mb-2">Design Tokens</h3>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            <Input
              placeholder="Search tokens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
            <TabsTrigger value="color" className="text-xs">Color</TabsTrigger>
            <TabsTrigger value="spacing" className="text-xs">Space</TabsTrigger>
            <TabsTrigger value="typography" className="text-xs">Type</TabsTrigger>
            <TabsTrigger value="border" className="text-xs">Border</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {Object.entries(tokensByCategory).map(([category, categoryTokens]) => (
            <Collapsible
              key={category}
              open={openCategories.has(category)}
              onOpenChange={() => toggleCategory(category)}
            >
              <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-accent rounded-md">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span className="font-medium text-sm capitalize">{category}</span>
                  <Badge variant="secondary" className="text-xs">
                    {categoryTokens.length}
                  </Badge>
                </div>
                {openCategories.has(category) ? (
                  <ChevronDown className="size-4" />
                ) : (
                  <ChevronRight className="size-4" />
                )}
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-2 space-y-2">
                {categoryTokens.map((token) => (
                  <TokenControl
                    key={token.cssVar}
                    token={token}
                    value={values[token.cssVar] || token.value}
                    onChange={(value) => onChange(token.cssVar, value)}
                    isRelevantToComponent={
                      !selectedComponent || 
                      selectedComponent.designTokens.includes(token.cssVar)
                    }
                  />
                ))}
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface TokenControlProps {
  token: DesignToken;
  value: string;
  onChange: (value: string) => void;
  isRelevantToComponent: boolean;
}

function TokenControl({ token, value, onChange, isRelevantToComponent }: TokenControlProps) {
  const renderControl = () => {
    switch (token.type) {
      case 'color':
        return (
          <div className="flex items-center space-x-2">
            <div
              className="w-6 h-6 rounded border flex-shrink-0"
              style={{ backgroundColor: value }}
            />
            <Input
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="font-mono text-xs"
              placeholder={token.value}
            />
          </div>
        );
      
      case 'spacing':
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="font-mono text-xs"
            placeholder={token.value}
          />
        );
      
      default:
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="font-mono text-xs"
            placeholder={token.value}
          />
        );
    }
  };

  return (
    <div className={cn(
      "p-3 border rounded-md space-y-2",
      isRelevantToComponent ? "border-primary/20 bg-primary/5" : "border-border"
    )}>
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium text-sm">{token.name}</div>
          <div className="text-xs text-muted-foreground font-mono">{token.cssVar}</div>
        </div>
        {isRelevantToComponent && (
          <Badge variant="outline" className="text-xs">
            Used
          </Badge>
        )}
      </div>
      {renderControl()}
    </div>
  );
}
