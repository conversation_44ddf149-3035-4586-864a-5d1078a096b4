"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Copy, Check, Download } from "lucide-react";
import { cn } from "@/lib/utils";

interface CodeExportPanelProps {
  componentName: string;
  props: Record<string, any>;
}

export function CodeExportPanel({ componentName, props }: CodeExportPanelProps) {
  const [copiedSection, setCopiedSection] = useState<string | null>(null);

  const generateCode = () => {
    // Generate component JSX
    const propsString = Object.entries(props)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `  ${key}="${value}"`;
        }
        if (typeof value === 'boolean') {
          return value ? `  ${key}` : '';
        }
        if (typeof value === 'number') {
          return `  ${key}={${value}}`;
        }
        return `  ${key}={${JSON.stringify(value)}}`;
      })
      .filter(Boolean)
      .join('\n');

    const componentCode = `<${componentName}${propsString ? `\n${propsString}` : ''}\n/>`;

    // Generate imports
    const imports = `import { ${componentName} } from "@/components/${componentName.toLowerCase()}";`;

    // Generate full page code
    const fullPageCode = `${imports}

export default function Page() {
  return (
    <div className="min-h-screen p-8">
      ${componentCode}
    </div>
  );
}`;

    return {
      component: componentCode,
      imports,
      fullPage: fullPageCode,
    };
  };

  const code = generateCode();

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const downloadCode = () => {
    const blob = new Blob([code.fullPage], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${componentName.toLowerCase()}-component.tsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Export Code</h3>
        <Button variant="outline" size="sm" onClick={downloadCode}>
          <Download className="h-4 w-4 mr-1" />
          Download
        </Button>
      </div>

      <Tabs defaultValue="component" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="component">Component</TabsTrigger>
          <TabsTrigger value="imports">Imports</TabsTrigger>
          <TabsTrigger value="full">Full Page</TabsTrigger>
        </TabsList>

        <TabsContent value="component" className="mt-4">
          <CodeBlock
            code={code.component}
            language="jsx"
            title="Component JSX"
            onCopy={() => copyToClipboard(code.component, 'component')}
            copied={copiedSection === 'component'}
          />
        </TabsContent>

        <TabsContent value="imports" className="mt-4">
          <CodeBlock
            code={code.imports}
            language="javascript"
            title="Import Statement"
            onCopy={() => copyToClipboard(code.imports, 'imports')}
            copied={copiedSection === 'imports'}
          />
        </TabsContent>

        <TabsContent value="full" className="mt-4">
          <CodeBlock
            code={code.fullPage}
            language="tsx"
            title="Complete Page"
            onCopy={() => copyToClipboard(code.fullPage, 'full')}
            copied={copiedSection === 'full'}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface CodeBlockProps {
  code: string;
  language: string;
  title: string;
  onCopy: () => void;
  copied: boolean;
}

function CodeBlock({ code, language, title, onCopy, copied }: CodeBlockProps) {
  return (
    <div className="border rounded-md">
      <div className="flex items-center justify-between p-3 border-b bg-muted/50">
        <span className="font-medium text-sm">{title}</span>
        <Button variant="outline" size="sm" onClick={onCopy}>
          {copied ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <Copy className="h-3 w-3 mr-1" />
          )}
          {copied ? 'Copied' : 'Copy'}
        </Button>
      </div>
      
      <ScrollArea className="h-64">
        <pre className="p-4 text-sm font-mono leading-relaxed">
          <code>{code}</code>
        </pre>
      </ScrollArea>
    </div>
  );
}
