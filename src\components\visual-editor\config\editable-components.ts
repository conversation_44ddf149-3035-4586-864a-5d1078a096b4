import { <PERSON> } from "@/components/hero";
import { <PERSON><PERSON> } from "@/components/login";
import { <PERSON>Header } from "@/components/brand-header";
import { BrandSidebar } from "@/components/brand-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import type { EditableComponent } from "../types";

export const editableComponents: EditableComponent[] = [
  // Hero Component
  {
    id: "hero",
    name: "Hero",
    type: "component",
    title: "Hero Section",
    description: "Attention-grabbing section for the top of your landing pages",
    component: Hero,
    properties: [
      {
        name: "title",
        type: "text",
        value: "Build a Registry",
        label: "Title",
        description: "Main heading text",
      },
      {
        name: "description",
        type: "text",
        value: "This starter helps you create a registry so you can distribute your custom components, hooks, pages, and other files to any React project",
        label: "Description",
        description: "Supporting description text",
      },
      {
        name: "buttonText",
        type: "text",
        value: "Learn more",
        label: "Button Text",
        description: "Call-to-action button text",
      },
      {
        name: "buttonLink",
        type: "text",
        value: "#sale",
        label: "Button Link",
        description: "URL for the button",
      },
      {
        name: "backgroundImage",
        type: "text",
        value: "/assets/hero.png",
        label: "Background Image",
        description: "Background image URL",
      },
    ],
    designTokens: [
      "--primary",
      "--primary-foreground",
      "--secondary",
      "--secondary-foreground",
      "--background",
      "--foreground",
      "--radius",
    ],
    defaultProps: {
      title: "Build a Registry",
      description: "This starter helps you create a registry so you can distribute your custom components, hooks, pages, and other files to any React project",
      buttonText: "Learn more",
      buttonLink: "#sale",
      backgroundImage: "/assets/hero.png",
    },
  },

  // Button Component
  {
    id: "button",
    name: "Button",
    type: "ui",
    title: "Button",
    description: "Allows users to take actions with a single click or tap",
    component: Button,
    properties: [
      {
        name: "children",
        type: "text",
        value: "Click me",
        label: "Text",
        description: "Button text content",
      },
      {
        name: "variant",
        type: "select",
        value: "default",
        options: ["default", "destructive", "outline", "secondary", "ghost", "link"],
        label: "Variant",
        description: "Button style variant",
      },
      {
        name: "size",
        type: "select",
        value: "default",
        options: ["default", "sm", "lg", "icon"],
        label: "Size",
        description: "Button size",
      },
      {
        name: "disabled",
        type: "boolean",
        value: false,
        label: "Disabled",
        description: "Whether the button is disabled",
      },
    ],
    variants: [
      {
        name: "primary",
        label: "Primary Button",
        props: { variant: "default", size: "default", children: "Primary Action" },
      },
      {
        name: "secondary",
        label: "Secondary Button",
        props: { variant: "secondary", size: "default", children: "Secondary Action" },
      },
      {
        name: "outline",
        label: "Outline Button",
        props: { variant: "outline", size: "default", children: "Outline Action" },
      },
      {
        name: "destructive",
        label: "Destructive Button",
        props: { variant: "destructive", size: "default", children: "Delete" },
      },
    ],
    designTokens: [
      "--primary",
      "--primary-foreground",
      "--secondary",
      "--secondary-foreground",
      "--destructive",
      "--destructive-foreground",
      "--border",
      "--radius",
    ],
    defaultProps: {
      children: "Click me",
      variant: "default",
      size: "default",
      disabled: false,
    },
  },

  // Badge Component
  {
    id: "badge",
    name: "Badge",
    type: "ui",
    title: "Badge",
    description: "Displays a small count or status indicator",
    component: Badge,
    properties: [
      {
        name: "children",
        type: "text",
        value: "Badge",
        label: "Text",
        description: "Badge text content",
      },
      {
        name: "variant",
        type: "select",
        value: "default",
        options: ["default", "secondary", "destructive", "outline"],
        label: "Variant",
        description: "Badge style variant",
      },
    ],
    variants: [
      {
        name: "default",
        label: "Default Badge",
        props: { variant: "default", children: "New" },
      },
      {
        name: "secondary",
        label: "Secondary Badge",
        props: { variant: "secondary", children: "Beta" },
      },
      {
        name: "outline",
        label: "Outline Badge",
        props: { variant: "outline", children: "Coming Soon" },
      },
      {
        name: "destructive",
        label: "Destructive Badge",
        props: { variant: "destructive", children: "Error" },
      },
    ],
    designTokens: [
      "--primary",
      "--primary-foreground",
      "--secondary",
      "--secondary-foreground",
      "--destructive",
      "--destructive-foreground",
      "--border",
      "--radius",
    ],
    defaultProps: {
      children: "Badge",
      variant: "default",
    },
  },

  // Card Component
  {
    id: "card",
    name: "Card",
    type: "ui",
    title: "Card",
    description: "Container for displaying content and actions about a single subject",
    component: ({ title, description, content, ...props }: any) => (
      <Card {...props}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <p>{content}</p>
        </CardContent>
      </Card>
    ),
    properties: [
      {
        name: "title",
        type: "text",
        value: "Card Title",
        label: "Title",
        description: "Card title text",
      },
      {
        name: "description",
        type: "text",
        value: "Card description goes here",
        label: "Description",
        description: "Card description text",
      },
      {
        name: "content",
        type: "text",
        value: "This is the main content of the card. You can put any information here.",
        label: "Content",
        description: "Main card content",
      },
    ],
    designTokens: [
      "--card",
      "--card-foreground",
      "--border",
      "--radius",
    ],
    defaultProps: {
      title: "Card Title",
      description: "Card description goes here",
      content: "This is the main content of the card. You can put any information here.",
    },
  },
];
