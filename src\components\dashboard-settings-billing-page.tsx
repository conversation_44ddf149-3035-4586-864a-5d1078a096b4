"use client"

import { useState } from "react"
import { DashboardButton } from "./dashboard-button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { ExternalLink } from "lucide-react"
import { cx } from "@/lib/dashboard-utils"

const billingData = [
  {
    name: "Starter plan",
    description: "Discounted plan for start-ups and growing companies",
    value: "$90",
  },
  {
    name: "Storage",
    description: "Used 10.1 GB",
    value: "$40",
    capacity: "100 GB included",
    percentageValue: 10.1,
  },
  {
    name: "Bandwidth",
    description: "Used 2.9 GB",
    value: "$10",
    capacity: "5 GB included",
    percentageValue: 58,
  },
  {
    name: "Users",
    description: "Used 9",
    value: "$20",
    capacity: "50 users included",
    percentageValue: 18,
  },
  {
    name: "Query super caching (EU-Central 1)",
    description: "4 GB query cache, $120/mo",
    value: "$120.00",
  },
]

export function DashboardSettingsBillingPage() {
  const [isSpendMgmtEnabled, setIsSpendMgmtEnabled] = useState(true)
  const [botProtection, setBotProtection] = useState(false)
  const [insights, setInsights] = useState(false)

  return (
    <div className="space-y-8">
      {/* Plan Status */}
      <div className="rounded-lg bg-blue-50 p-6 ring-1 ring-inset ring-blue-200 dark:bg-blue-400/10 dark:ring-blue-800">
        <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
          This workspace is currently on free plan
        </h4>
        <p className="mt-1 max-w-2xl text-sm leading-6 text-blue-700 dark:text-blue-200">
          Boost your analytics and unlock advanced features with our premium
          plans.{" "}
          <a
            href="#"
            className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400"
          >
            Compare plans
            <ExternalLink className="size-4 shrink-0" />
          </a>
        </p>
      </div>

      {/* Billing Overview */}
      <section aria-labelledby="billing-overview">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2
              id="billing-overview"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Billing
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Overview of current billing cycle based on fixed and on-demand
              charges.
            </p>
          </div>
          <div className="md:col-span-2">
            <Card>
              <CardContent className="p-0">
                <ul className="divide-y divide-gray-200 dark:divide-gray-800">
                  {billingData.map((item) => (
                    <li key={item.name} className="px-4 py-4 text-sm">
                      <div className="w-full">
                        <div className="flex items-center justify-between">
                          <p className="font-medium text-gray-900 dark:text-gray-50">
                            {item.name}
                          </p>
                          <p className="font-medium text-gray-700 dark:text-gray-300">
                            {item.value}
                          </p>
                        </div>
                        <div className="w-full md:w-2/3">
                          {item.percentageValue && (
                            <Progress
                              value={item.percentageValue}
                              className="mt-2 h-1.5"
                            />
                          )}
                          <p className="mt-1 flex items-center justify-between text-xs text-gray-500">
                            <span>{item.description}</span>
                            <span>{item.capacity}</span>
                          </p>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
                <div className="border-t border-gray-200 px-4 py-4 dark:border-gray-800">
                  <p className="flex items-center justify-between text-sm font-medium text-gray-900 dark:text-gray-50">
                    <span>Total for May 24</span>
                    <span className="font-semibold">$280</span>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Separator />

      {/* Cost Spend Control */}
      <section aria-labelledby="cost-spend-control">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2
              id="cost-spend-control"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Cost spend control
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Set hard caps for on-demand charges.
            </p>
          </div>
          <div className="md:col-span-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="relative size-16">
                      <svg className="size-16 -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="m18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeDasharray={`${isSpendMgmtEnabled ? 62.2 : 0}, 100`}
                          className="text-indigo-600 dark:text-indigo-500"
                        />
                        <path
                          d="m18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="text-gray-200 dark:text-gray-800"
                        />
                      </svg>
                    </div>
                    <div>
                      {isSpendMgmtEnabled ? (
                        <>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-50">
                            $280 / 350 (62.2%)
                          </p>
                          <Label className="text-gray-500">
                            Spend management enabled
                          </Label>
                        </>
                      ) : (
                        <>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-50">
                            $0 / 0 (0%)
                          </p>
                          <Label className="text-gray-500">
                            Spend management disabled
                          </Label>
                        </>
                      )}
                    </div>
                  </div>
                  <Switch
                    checked={isSpendMgmtEnabled}
                    onCheckedChange={setIsSpendMgmtEnabled}
                  />
                </div>
                
                <div
                  className={cx(
                    "transform-gpu transition-all duration-300 ease-out overflow-hidden",
                    isSpendMgmtEnabled ? "max-h-52 opacity-100" : "max-h-0 opacity-0"
                  )}
                >
                  <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div className="md:col-span-1">
                      <Label className="font-medium">Set amount ($)</Label>
                      <Input
                        defaultValue="350"
                        type="number"
                        className="mt-2"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label className="font-medium">
                        Provide email for notifications
                      </Label>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        className="mt-2"
                      />
                    </div>
                  </div>
                  <div className="mt-6 flex justify-end">
                    <DashboardButton>Update</DashboardButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Separator />

      {/* Add-Ons */}
      <section aria-labelledby="add-ons">
        <div className="grid grid-cols-1 gap-x-14 gap-y-8 md:grid-cols-3">
          <div>
            <h2
              id="add-ons"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Add-Ons
            </h2>
            <p className="mt-1 text-sm leading-6 text-gray-500">
              Additional services to boost your services.
            </p>
          </div>
          <div className="space-y-6 md:col-span-2">
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="px-4 pb-6 pt-4">
                  <span className="text-sm text-gray-500">$25/month</span>
                  <h4 className="mt-4 text-sm font-semibold text-gray-900 dark:text-gray-50">
                    Advanced bot protection
                  </h4>
                  <p className="mt-2 max-w-xl text-sm leading-6 text-gray-500">
                    Safeguard your assets with our cutting-edge bot
                    protection. Our AI solution identifies and mitigates
                    automated traffic to protect your workspace from bad bots.
                  </p>
                </div>
                <div className="flex items-center justify-between border-t border-gray-200 bg-gray-50 p-4 dark:border-gray-900 dark:bg-gray-900">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={botProtection}
                      onCheckedChange={setBotProtection}
                    />
                    <Label>Activate</Label>
                  </div>
                  <a
                    href="#"
                    className="inline-flex items-center gap-1 text-sm text-indigo-600 dark:text-indigo-500"
                  >
                    Learn more
                    <ExternalLink className="size-4 shrink-0" />
                  </a>
                </div>
              </CardContent>
            </Card>
            
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="px-4 pb-6 pt-4">
                  <span className="text-sm text-gray-500">$50/month</span>
                  <h4 className="mt-4 text-sm font-semibold text-gray-900 dark:text-gray-50">
                    Workspace insights
                  </h4>
                  <p className="mt-2 max-w-xl text-sm leading-6 text-gray-500">
                    Real-time analysis of your workspace's usage, enabling
                    you to make well-informed decisions for optimization.
                  </p>
                </div>
                <div className="flex items-center justify-between border-t border-gray-200 bg-gray-50 p-4 dark:border-gray-900 dark:bg-gray-900">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={insights}
                      onCheckedChange={setInsights}
                    />
                    <Label>Activate</Label>
                  </div>
                  <a
                    href="#"
                    className="inline-flex items-center gap-1 text-sm text-indigo-600 dark:text-indigo-500"
                  >
                    Learn more
                    <ExternalLink className="size-4 shrink-0" />
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
