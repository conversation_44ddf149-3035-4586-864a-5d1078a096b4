"use client";

import { TextControl } from "./text-control";
import { ColorControl } from "./color-control";
import { NumberControl } from "./number-control";
import { SelectControl } from "./select-control";
import { BooleanControl } from "./boolean-control";
import { SpacingControl } from "./spacing-control";
import { TypographyControl } from "./typography-control";
import type { PropertyControl } from "../types";

export const propertyControls: Record<string, PropertyControl> = {
  text: {
    type: 'text',
    component: TextControl,
  },
  color: {
    type: 'color',
    component: ColorControl,
  },
  number: {
    type: 'number',
    component: NumberControl,
  },
  select: {
    type: 'select',
    component: SelectControl,
  },
  boolean: {
    type: 'boolean',
    component: BooleanControl,
  },
  spacing: {
    type: 'spacing',
    component: SpacingControl,
  },
  typography: {
    type: 'typography',
    component: TypographyControl,
  },
};

export {
  TextControl,
  ColorControl,
  NumberControl,
  SelectControl,
  BooleanControl,
  SpacingControl,
  TypographyControl,
};
