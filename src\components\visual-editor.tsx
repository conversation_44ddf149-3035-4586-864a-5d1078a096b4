"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Copy, Download, RotateCcw, Eye } from "lucide-react"
import { toast } from "sonner"

// Color palette for easy selection
const colorPalette = [
  { name: "Indigo", value: "indigo", css: "bg-indigo-500" },
  { name: "Blue", value: "blue", css: "bg-blue-500" },
  { name: "<PERSON>", value: "green", css: "bg-green-500" },
  { name: "Purple", value: "purple", css: "bg-purple-500" },
  { name: "Pink", value: "pink", css: "bg-pink-500" },
  { name: "Red", value: "red", css: "bg-red-500" },
  { name: "Orange", value: "orange", css: "bg-orange-500" },
  { name: "Yellow", value: "yellow", css: "bg-yellow-500" },
  { name: "Gray", value: "gray", css: "bg-gray-500" },
  { name: "Slate", value: "slate", css: "bg-slate-500" },
]

const sizePalette = [
  { name: "Small", value: "sm" },
  { name: "Medium", value: "md" },
  { name: "Large", value: "lg" },
  { name: "Extra Large", value: "xl" },
]

// Define editable properties for different component types
const componentSchema = {
  "dashboard-button": {
    title: "Dashboard Button",
    props: {
      variant: {
        type: "select",
        label: "Variant",
        options: [
          { label: "Primary", value: "primary" },
          { label: "Secondary", value: "secondary" },
          { label: "Ghost", value: "ghost" },
          { label: "Destructive", value: "destructive" },
        ],
        default: "primary"
      },
      children: {
        type: "text",
        label: "Button Text",
        default: "Button"
      },
      size: {
        type: "select", 
        label: "Size",
        options: sizePalette,
        default: "md"
      },
      disabled: {
        type: "boolean",
        label: "Disabled",
        default: false
      },
      isLoading: {
        type: "boolean",
        label: "Loading State",
        default: false
      }
    }
  },
  "dashboard-card": {
    title: "Dashboard Card",
    props: {
      title: {
        type: "text",
        label: "Card Title",
        default: "Dashboard Card"
      },
      description: {
        type: "text",
        label: "Description",
        default: "Card description goes here"
      },
      color: {
        type: "color",
        label: "Primary Color",
        default: "indigo"
      },
      size: {
        type: "select",
        label: "Size",
        options: sizePalette,
        default: "md"
      }
    }
  },
  "login": {
    title: "Login Form",
    props: {
      title: {
        type: "text", 
        label: "Form Title",
        default: "Welcome back"
      },
      subtitle: {
        type: "text",
        label: "Subtitle",
        default: "Enter your email to sign in to your account"
      },
      brandName: {
        type: "text",
        label: "Brand Name", 
        default: "Brand Name"
      },
      testimonialText: {
        type: "textarea",
        label: "Testimonial",
        default: "Tbh @shadcn really cooked with @shadcn. Keeps passing the test of time."
      },
      testimonialAuthor: {
        type: "text",
        label: "Testimonial Author",
        default: "Guillermo Rauch, CEO of Vercel"
      },
      primaryColor: {
        type: "color",
        label: "Primary Color",
        default: "green"
      }
    }
  },
  "complete-dashboard": {
    title: "Complete Dashboard",
    props: {
      workspaceName: {
        type: "text",
        label: "Workspace Name",
        default: "My Workspace"
      },
      userName: {
        type: "text",
        label: "User Name", 
        default: "John Doe"
      },
      userEmail: {
        type: "text",
        label: "User Email",
        default: "<EMAIL>"
      },
      primaryColor: {
        type: "color",
        label: "Primary Color",
        default: "indigo"
      },
      sidebarWidth: {
        type: "select",
        label: "Sidebar Width",
        options: [
          { label: "Narrow", value: "narrow" },
          { label: "Normal", value: "normal" },
          { label: "Wide", value: "wide" }
        ],
        default: "normal"
      }
    }
  }
}

interface VisualEditorProps {
  componentName: string
  initialProps?: Record<string, any>
  onPropsChange?: (props: Record<string, any>) => void
}

export function VisualEditor({ 
  componentName, 
  initialProps = {}, 
  onPropsChange 
}: VisualEditorProps) {
  const schema = componentSchema[componentName as keyof typeof componentSchema]
  const [props, setProps] = useState(() => {
    const defaultProps: Record<string, any> = {}
    if (schema) {
      Object.entries(schema.props).forEach(([key, config]) => {
        defaultProps[key] = initialProps[key] ?? config.default
      })
    }
    return defaultProps
  })
  
  const [activeTab, setActiveTab] = useState("style")

  useEffect(() => {
    onPropsChange?.(props)
  }, [props, onPropsChange])

  const updateProp = (key: string, value: any) => {
    setProps(prev => ({ ...prev, [key]: value }))
  }

  const resetToDefaults = () => {
    if (schema) {
      const defaultProps: Record<string, any> = {}
      Object.entries(schema.props).forEach(([key, config]) => {
        defaultProps[key] = config.default
      })
      setProps(defaultProps)
    }
  }

  const copyPropsAsCode = () => {
    const propsString = Object.entries(props)
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `${key}="${value}"`
        }
        return `${key}={${JSON.stringify(value)}}`
      })
      .join('\n  ')
    
    const code = `<${schema?.title.replace(/\s+/g, '')} 
  ${propsString}
/>`
    
    navigator.clipboard.writeText(code)
    toast.success("Component code copied to clipboard!")
  }

  const exportAsJSON = () => {
    const exportData = {
      component: componentName,
      props: props,
      timestamp: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${componentName}-config.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (!schema) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-gray-500">
            Visual editor not available for component: {componentName}
          </p>
        </CardContent>
      </Card>
    )
  }

  const renderPropertyControl = (key: string, config: any) => {
    const value = props[key]

    switch (config.type) {
      case "text":
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{config.label}</Label>
            <Input
              id={key}
              value={value || ""}
              onChange={(e) => updateProp(key, e.target.value)}
              placeholder={config.default}
            />
          </div>
        )

      case "textarea":
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{config.label}</Label>
            <textarea
              id={key}
              value={value || ""}
              onChange={(e) => updateProp(key, e.target.value)}
              placeholder={config.default}
              className="w-full min-h-[80px] px-3 py-2 border border-gray-200 rounded-md resize-y dark:border-gray-800"
            />
          </div>
        )

      case "select":
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{config.label}</Label>
            <Select value={value} onValueChange={(val) => updateProp(key, val)}>
              <SelectTrigger>
                <SelectValue placeholder={`Select ${config.label}`} />
              </SelectTrigger>
              <SelectContent>
                {config.options.map((option: any) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label || option.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )

      case "color":
        return (
          <div key={key} className="space-y-2">
            <Label>{config.label}</Label>
            <div className="grid grid-cols-5 gap-2">
              {colorPalette.map((color) => (
                <button
                  key={color.value}
                  onClick={() => updateProp(key, color.value)}
                  className={`
                    w-8 h-8 rounded-md border-2 transition-all
                    ${color.css}
                    ${value === color.value 
                      ? 'border-gray-900 dark:border-gray-100 scale-110' 
                      : 'border-gray-300 dark:border-gray-700 hover:scale-105'
                    }
                  `}
                  title={color.name}
                />
              ))}
            </div>
            <div className="text-xs text-gray-500">
              Selected: <Badge variant="outline">{value}</Badge>
            </div>
          </div>
        )

      case "boolean":
        return (
          <div key={key} className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={key}
              checked={value || false}
              onChange={(e) => updateProp(key, e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={key}>{config.label}</Label>
          </div>
        )

      default:
        return null
    }
  }

  const styleProps = Object.entries(schema.props).filter(([_, config]) => 
    config.type === 'color' || config.type === 'select'
  )
  
  const contentProps = Object.entries(schema.props).filter(([_, config]) => 
    config.type === 'text' || config.type === 'textarea'
  )
  
  const behaviorProps = Object.entries(schema.props).filter(([_, config]) => 
    config.type === 'boolean'
  )

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Visual Editor</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={resetToDefaults}
              className="h-8"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={copyPropsAsCode}
              className="h-8"
            >
              <Copy className="w-4 h-4 mr-1" />
              Copy Code
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportAsJSON}
              className="h-8"
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="style">Style</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="behavior">Behavior</TabsTrigger>
          </TabsList>
          
          <TabsContent value="style" className="space-y-4 mt-4">
            {styleProps.length > 0 ? (
              styleProps.map(([key, config]) => renderPropertyControl(key, config))
            ) : (
              <p className="text-gray-500 text-sm">No style properties available</p>
            )}
          </TabsContent>
          
          <TabsContent value="content" className="space-y-4 mt-4">
            {contentProps.length > 0 ? (
              contentProps.map(([key, config]) => renderPropertyControl(key, config))
            ) : (
              <p className="text-gray-500 text-sm">No content properties available</p>
            )}
          </TabsContent>
          
          <TabsContent value="behavior" className="space-y-4 mt-4">
            {behaviorProps.length > 0 ? (
              behaviorProps.map(([key, config]) => renderPropertyControl(key, config))
            ) : (
              <p className="text-gray-500 text-sm">No behavior properties available</p>
            )}
          </TabsContent>
        </Tabs>
        
        <Separator className="my-4" />
        
        <div className="space-y-2">
          <Label className="text-sm font-medium">Current Configuration</Label>
          <pre className="text-xs bg-gray-50 dark:bg-gray-900 p-3 rounded-md overflow-x-auto">
            {JSON.stringify(props, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  )
}
