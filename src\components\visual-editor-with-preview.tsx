"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { VisualEditor } from "./visual-editor"
import { DashboardButton } from "./dashboard-button"
import { Eye, Code2, Palette } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Component registry for live preview
const ComponentRegistry = {
  "dashboard-button": DashboardButton,
  // Add more components as needed
}

// Sample components for preview
const SampleLoginComponent = ({ 
  title = "Welcome back",
  subtitle = "Enter your email to sign in to your account", 
  brandName = "Brand Name",
  testimonialText = "Tbh @shadcn really cooked with @shadcn. Keeps passing the test of time.",
  testimonialAuthor = "<PERSON>, CEO of Vercel",
  primaryColor = "green"
}: any) => (
  <div className="min-h-[500px] w-full flex">
    {/* Left side - Brand/Testimonial */}
    <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-between lg:bg-gray-100 lg:p-8 dark:lg:bg-gray-900">
      <div>
        <div className="flex items-center gap-2">
          <div className={`w-6 h-6 rounded-full bg-${primaryColor}-500`}></div>
          <span className="font-semibold">{brandName}</span>
        </div>
      </div>
      
      <div className="space-y-4">
        <blockquote className="text-lg font-medium">
          "{testimonialText}"
        </blockquote>
        <cite className="text-sm text-gray-600 dark:text-gray-400">
          {testimonialAuthor}
        </cite>
      </div>
    </div>
    
    {/* Right side - Login Form */}
    <div className="flex flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        <h2 className="text-2xl font-bold">{title}</h2>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{subtitle}</p>
        
        <div className="mt-8 space-y-4">
          <div>
            <label className="block text-sm font-medium">Email</label>
            <input 
              type="email" 
              placeholder="<EMAIL>"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium">Password</label>
            <input 
              type="password"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <Button className={`w-full bg-${primaryColor}-600 hover:bg-${primaryColor}-700`}>
            Sign in
          </Button>
          
          <div className="text-center text-sm">
            OR CONTINUE WITH
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline">Google</Button>
            <Button variant="outline">Microsoft</Button>
          </div>
        </div>
      </div>
    </div>
  </div>
)

const SampleDashboardCard = ({ 
  title = "Dashboard Card",
  description = "Card description goes here",
  color = "indigo",
  size = "md"
}: any) => {
  const sizeClasses = {
    sm: "p-4",
    md: "p-6", 
    lg: "p-8",
    xl: "p-10"
  }
  
  return (
    <Card className="w-full max-w-sm">
      <CardHeader className={sizeClasses[size]}>
        <div className="flex items-center gap-3">
          <div className={`w-4 h-4 rounded-full bg-${color}-500`}></div>
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className={sizeClasses[size]}>
        <p className="text-gray-600 dark:text-gray-400">{description}</p>
        <div className="mt-4 flex items-center justify-between">
          <span className="text-2xl font-bold">1,234</span>
          <Badge className={`bg-${color}-100 text-${color}-800 dark:bg-${color}-900 dark:text-${color}-200`}>
            +12%
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}

const SampleComponents = {
  "dashboard-button": DashboardButton,
  "login": SampleLoginComponent,
  "dashboard-card": SampleDashboardCard,
  "complete-dashboard": () => (
    <div className="w-full h-96 bg-gray-100 dark:bg-gray-900 rounded-lg flex items-center justify-center">
      <p className="text-gray-500">Complete Dashboard Preview</p>
    </div>
  )
}

interface ComponentPreviewProps {
  componentName: string
  props: Record<string, any>
}

function ComponentPreview({ componentName, props }: ComponentPreviewProps) {
  const Component = SampleComponents[componentName as keyof typeof SampleComponents]
  
  if (!Component) {
    return (
      <div className="w-full h-64 bg-gray-50 dark:bg-gray-900 rounded-lg flex items-center justify-center">
        <p className="text-gray-500">Preview not available for {componentName}</p>
      </div>
    )
  }
  
  return (
    <div className="w-full">
      <Component {...props} />
    </div>
  )
}

interface VisualEditorWithPreviewProps {
  componentName: string
  initialProps?: Record<string, any>
}

export function VisualEditorWithPreview({ 
  componentName, 
  initialProps = {} 
}: VisualEditorWithPreviewProps) {
  const [props, setProps] = useState(initialProps)
  const [activeView, setActiveView] = useState("split")

  const generateCode = () => {
    const propsString = Object.entries(props)
      .filter(([_, value]) => value !== undefined && value !== "")
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `  ${key}="${value}"`
        }
        if (typeof value === 'boolean' && value) {
          return `  ${key}`
        }
        if (typeof value === 'boolean' && !value) {
          return "" // Don't include false boolean props
        }
        return `  ${key}={${JSON.stringify(value)}}`
      })
      .filter(Boolean)
      .join('\n')
    
    const componentDisplayName = componentName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('')
    
    return `<${componentDisplayName}${propsString ? '\n' + propsString + '\n' : ''}/>`
  }

  if (activeView === "editor") {
    return (
      <div className="w-full">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold">Visual Editor</h2>
          <Tabs value={activeView} onValueChange={setActiveView}>
            <TabsList>
              <TabsTrigger value="split" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Split
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="editor" className="flex items-center gap-2">
                <Code2 className="w-4 h-4" />
                Editor
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <VisualEditor 
          componentName={componentName}
          initialProps={initialProps}
          onPropsChange={setProps}
        />
      </div>
    )
  }

  if (activeView === "preview") {
    return (
      <div className="w-full">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold">Live Preview</h2>
          <Tabs value={activeView} onValueChange={setActiveView}>
            <TabsList>
              <TabsTrigger value="split" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Split
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="editor" className="flex items-center gap-2">
                <Code2 className="w-4 h-4" />
                Editor
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <Card>
          <CardContent className="p-8">
            <ComponentPreview componentName={componentName} props={props} />
          </CardContent>
        </Card>
        
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="text-lg">Generated Code</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm">
              <code>{generateCode()}</code>
            </pre>
            <Button 
              className="mt-3" 
              onClick={() => {
                navigator.clipboard.writeText(generateCode())
                // You'd need to add toast notification here
              }}
            >
              Copy Code
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Split view (default)
  return (
    <div className="w-full">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold">Visual Editor</h2>
        <Tabs value={activeView} onValueChange={setActiveView}>
          <TabsList>
            <TabsTrigger value="split" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Split
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Code2 className="w-4 h-4" />
              Editor
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Editor Panel */}
        <div>
          <VisualEditor 
            componentName={componentName}
            initialProps={initialProps}
            onPropsChange={setProps}
          />
        </div>
        
        {/* Preview Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Live Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ComponentPreview componentName={componentName} props={props} />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code2 className="w-5 h-5" />
                Generated Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm">
                <code>{generateCode()}</code>
              </pre>
              <Button 
                className="mt-3" 
                onClick={() => {
                  navigator.clipboard.writeText(generateCode())
                  // Add toast notification here if needed
                }}
              >
                Copy Code
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
