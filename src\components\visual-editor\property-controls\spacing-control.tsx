"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Link, Unlink } from "lucide-react";
import { cn } from "@/lib/utils";
import type { PropertyControlProps } from "../types";

interface SpacingValue {
  top: number;
  right: number;
  bottom: number;
  left: number;
  linked: boolean;
}

export function SpacingControl({ property, value, onChange }: PropertyControlProps) {
  const [mode, setMode] = useState<'all' | 'individual'>('all');
  
  // Parse value - could be number, string, or object
  const parseSpacingValue = (val: any): SpacingValue => {
    if (typeof val === 'number') {
      return { top: val, right: val, bottom: val, left: val, linked: true };
    }
    if (typeof val === 'string') {
      const num = parseFloat(val) || 0;
      return { top: num, right: num, bottom: num, left: num, linked: true };
    }
    if (typeof val === 'object' && val !== null) {
      return {
        top: val.top || 0,
        right: val.right || 0,
        bottom: val.bottom || 0,
        left: val.left || 0,
        linked: val.linked ?? true,
      };
    }
    return { top: 0, right: 0, bottom: 0, left: 0, linked: true };
  };

  const spacingValue = parseSpacingValue(value);

  const handleAllChange = (newValue: number) => {
    onChange({
      top: newValue,
      right: newValue,
      bottom: newValue,
      left: newValue,
      linked: true,
    });
  };

  const handleIndividualChange = (side: keyof Omit<SpacingValue, 'linked'>, newValue: number) => {
    if (spacingValue.linked) {
      handleAllChange(newValue);
    } else {
      onChange({
        ...spacingValue,
        [side]: newValue,
      });
    }
  };

  const toggleLinked = () => {
    onChange({
      ...spacingValue,
      linked: !spacingValue.linked,
    });
  };

  const spacingPresets = [0, 4, 8, 12, 16, 20, 24, 32, 40, 48];

  return (
    <div className="space-y-3">
      {/* Presets */}
      <div className="grid grid-cols-5 gap-1">
        {spacingPresets.map((preset) => (
          <Button
            key={preset}
            variant={spacingValue.top === preset && spacingValue.linked ? "default" : "outline"}
            size="sm"
            className="h-8 text-xs"
            onClick={() => handleAllChange(preset)}
          >
            {preset}
          </Button>
        ))}
      </div>

      <Tabs value={mode} onValueChange={(v) => setMode(v as 'all' | 'individual')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
          <TabsTrigger value="individual" className="text-xs">Individual</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-2">
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              value={spacingValue.top}
              onChange={(e) => handleAllChange(parseFloat(e.target.value) || 0)}
              className="text-center font-mono"
              min={0}
            />
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={toggleLinked}
            >
              {spacingValue.linked ? (
                <Link className="h-3 w-3" />
              ) : (
                <Unlink className="h-3 w-3" />
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="individual" className="space-y-2">
          <div className="grid grid-cols-3 gap-2 items-center">
            {/* Top */}
            <div></div>
            <Input
              type="number"
              value={spacingValue.top}
              onChange={(e) => handleIndividualChange('top', parseFloat(e.target.value) || 0)}
              className="text-center font-mono text-xs h-8"
              placeholder="T"
              min={0}
            />
            <div></div>

            {/* Left and Right */}
            <Input
              type="number"
              value={spacingValue.left}
              onChange={(e) => handleIndividualChange('left', parseFloat(e.target.value) || 0)}
              className="text-center font-mono text-xs h-8"
              placeholder="L"
              min={0}
            />
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={toggleLinked}
            >
              {spacingValue.linked ? (
                <Link className="h-3 w-3" />
              ) : (
                <Unlink className="h-3 w-3" />
              )}
            </Button>
            <Input
              type="number"
              value={spacingValue.right}
              onChange={(e) => handleIndividualChange('right', parseFloat(e.target.value) || 0)}
              className="text-center font-mono text-xs h-8"
              placeholder="R"
              min={0}
            />

            {/* Bottom */}
            <div></div>
            <Input
              type="number"
              value={spacingValue.bottom}
              onChange={(e) => handleIndividualChange('bottom', parseFloat(e.target.value) || 0)}
              className="text-center font-mono text-xs h-8"
              placeholder="B"
              min={0}
            />
            <div></div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
