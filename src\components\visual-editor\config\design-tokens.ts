import type { DesignToken } from "../types";

export const designTokens: DesignToken[] = [
  // Color Tokens
  {
    name: "Primary",
    value: "oklch(0.52 0.13 144.17)",
    type: "color",
    category: "color",
    cssVar: "--primary",
  },
  {
    name: "Primary Foreground",
    value: "oklch(1.0 0 0)",
    type: "color",
    category: "color",
    cssVar: "--primary-foreground",
  },
  {
    name: "Secondary",
    value: "oklch(0.96 0.02 147.64)",
    type: "color",
    category: "color",
    cssVar: "--secondary",
  },
  {
    name: "Secondary Foreground",
    value: "oklch(0.43 0.12 144.31)",
    type: "color",
    category: "color",
    cssVar: "--secondary-foreground",
  },
  {
    name: "Background",
    value: "oklch(0.97 0.01 80.72)",
    type: "color",
    category: "color",
    cssVar: "--background",
  },
  {
    name: "Foreground",
    value: "oklch(0.3 0.04 30.2)",
    type: "color",
    category: "color",
    cssVar: "--foreground",
  },
  {
    name: "Card",
    value: "oklch(0.97 0.01 80.72)",
    type: "color",
    category: "color",
    cssVar: "--card",
  },
  {
    name: "Card Foreground",
    value: "oklch(0.3 0.04 30.2)",
    type: "color",
    category: "color",
    cssVar: "--card-foreground",
  },
  {
    name: "Muted",
    value: "oklch(0.94 0.01 74.42)",
    type: "color",
    category: "color",
    cssVar: "--muted",
  },
  {
    name: "Muted Foreground",
    value: "oklch(0.45 0.05 39.21)",
    type: "color",
    category: "color",
    cssVar: "--muted-foreground",
  },
  {
    name: "Accent",
    value: "oklch(0.9 0.05 146.04)",
    type: "color",
    category: "color",
    cssVar: "--accent",
  },
  {
    name: "Accent Foreground",
    value: "oklch(0.43 0.12 144.31)",
    type: "color",
    category: "color",
    cssVar: "--accent-foreground",
  },
  {
    name: "Destructive",
    value: "oklch(0.54 0.19 26.72)",
    type: "color",
    category: "color",
    cssVar: "--destructive",
  },
  {
    name: "Destructive Foreground",
    value: "oklch(1.0 0 0)",
    type: "color",
    category: "color",
    cssVar: "--destructive-foreground",
  },
  {
    name: "Border",
    value: "oklch(0.88 0.02 74.64)",
    type: "color",
    category: "color",
    cssVar: "--border",
  },
  {
    name: "Input",
    value: "oklch(0.88 0.02 74.64)",
    type: "color",
    category: "color",
    cssVar: "--input",
  },
  {
    name: "Ring",
    value: "oklch(0.52 0.13 144.17)",
    type: "color",
    category: "color",
    cssVar: "--ring",
  },

  // Border Tokens
  {
    name: "Border Radius",
    value: "0.5rem",
    type: "border",
    category: "border",
    cssVar: "--radius",
  },

  // Chart Colors
  {
    name: "Chart 1",
    value: "oklch(0.67 0.16 144.21)",
    type: "color",
    category: "chart",
    cssVar: "--chart-1",
  },
  {
    name: "Chart 2",
    value: "oklch(0.58 0.14 144.18)",
    type: "color",
    category: "chart",
    cssVar: "--chart-2",
  },
  {
    name: "Chart 3",
    value: "oklch(0.52 0.13 144.17)",
    type: "color",
    category: "chart",
    cssVar: "--chart-3",
  },
  {
    name: "Chart 4",
    value: "oklch(0.43 0.12 144.31)",
    type: "color",
    category: "chart",
    cssVar: "--chart-4",
  },
  {
    name: "Chart 5",
    value: "oklch(0.22 0.05 145.73)",
    type: "color",
    category: "chart",
    cssVar: "--chart-5",
  },

  // Sidebar Tokens
  {
    name: "Sidebar Background",
    value: "oklch(0.94 0.01 74.42)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar",
  },
  {
    name: "Sidebar Foreground",
    value: "oklch(0.3 0.04 30.2)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-foreground",
  },
  {
    name: "Sidebar Primary",
    value: "oklch(0.52 0.13 144.17)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-primary",
  },
  {
    name: "Sidebar Primary Foreground",
    value: "oklch(1.0 0 0)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-primary-foreground",
  },
  {
    name: "Sidebar Accent",
    value: "oklch(0.9 0.05 146.04)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-accent",
  },
  {
    name: "Sidebar Accent Foreground",
    value: "oklch(0.43 0.12 144.31)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-accent-foreground",
  },
  {
    name: "Sidebar Border",
    value: "oklch(0.88 0.02 74.64)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-border",
  },
  {
    name: "Sidebar Ring",
    value: "oklch(0.52 0.13 144.17)",
    type: "color",
    category: "sidebar",
    cssVar: "--sidebar-ring",
  },
];
