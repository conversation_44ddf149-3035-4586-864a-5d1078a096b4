import { VisualEditorWithPreview } from "@/components/visual-editor-with-preview"
import { getRegistryItem } from "@/lib/registry"
import { Button } from "@/components/ui/button"
import { ArrowLeft, ExternalLink } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"

interface EditorPageProps {
  params: {
    component: string
  }
}

export default function VisualEditorPage({ params }: EditorPageProps) {
  try {
    const component = getRegistryItem(params.component)
    
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link 
                  href={`/registry/${params.component}`}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                >
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Back to Component
                </Link>
                <div className="h-4 border-l border-gray-300 dark:border-gray-700" />
                <h1 className="text-xl font-semibold">
                  Visual Editor - {component.title}
                </h1>
              </div>
              
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/demo/${params.component}`}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Full Demo
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8">
          <VisualEditorWithPreview 
            componentName={params.component}
            initialProps={{}}
          />
        </div>
      </div>
    )
  } catch (error) {
    notFound()
  }
}

export async function generateStaticParams() {
  // You could generate static params for known components
  return [
    { component: 'dashboard-button' },
    { component: 'login' },
    { component: 'dashboard-card' },
    { component: 'complete-dashboard' },
  ]
}
