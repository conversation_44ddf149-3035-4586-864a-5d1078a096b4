:root {
  --background: oklch(0.97 0.01 80.72);
  --foreground: oklch(0.3 0.04 30.2);

  --card: oklch(0.97 0.01 80.72);
  --card-foreground: oklch(0.3 0.04 30.2);

  --popover: oklch(0.97 0.01 80.72);
  --popover-foreground: oklch(0.3 0.04 30.2);

  --primary: oklch(0.52 0.13 144.17);
  --primary-foreground: oklch(1.0 0 0);

  --secondary: oklch(0.96 0.02 147.64);
  --secondary-foreground: oklch(0.43 0.12 144.31);

  --muted: oklch(0.94 0.01 74.42);
  --muted-foreground: oklch(0.45 0.05 39.21);

  --accent: oklch(0.9 0.05 146.04);
  --accent-foreground: oklch(0.43 0.12 144.31);

  --destructive: oklch(0.54 0.19 26.72);
  --destructive-foreground: oklch(1.0 0 0);

  --border: oklch(0.88 0.02 74.64);
  --input: oklch(0.88 0.02 74.64);
  --ring: oklch(0.52 0.13 144.17);

  --chart-1: oklch(0.67 0.16 144.21);
  --chart-2: oklch(0.58 0.14 144.18);
  --chart-3: oklch(0.52 0.13 144.17);
  --chart-4: oklch(0.43 0.12 144.31);
  --chart-5: oklch(0.22 0.05 145.73);

  --sidebar: oklch(0.94 0.01 74.42);
  --sidebar-foreground: oklch(0.3 0.04 30.2);
  --sidebar-primary: oklch(0.52 0.13 144.17);
  --sidebar-primary-foreground: oklch(1.0 0 0);
  --sidebar-accent: oklch(0.9 0.05 146.04);
  --sidebar-accent-foreground: oklch(0.43 0.12 144.31);
  --sidebar-border: oklch(0.88 0.02 74.64);
  --sidebar-ring: oklch(0.52 0.13 144.17);

  --radius: 0.5rem;

  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px
    hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.15 0.01 74.42);
  --foreground: oklch(0.95 0.02 80.72);

  --card: oklch(0.12 0.01 80.72);
  --card-foreground: oklch(0.95 0.02 80.72);

  --popover: oklch(0.12 0.01 80.72);
  --popover-foreground: oklch(0.95 0.02 80.72);

  --primary: oklch(0.58 0.15 144.17);
  --primary-foreground: oklch(0.09 0.01 80.72);

  --secondary: oklch(0.15 0.02 147.64);
  --secondary-foreground: oklch(0.65 0.12 144.31);

  --muted: oklch(0.18 0.01 74.42);
  --muted-foreground: oklch(0.65 0.03 74.42);

  --accent: oklch(0.22 0.04 146.04);
  --accent-foreground: oklch(0.65 0.12 144.31);

  --destructive: oklch(0.62 0.22 26.72);
  --destructive-foreground: oklch(0.95 0.02 80.72);

  --border: oklch(0.22 0.02 74.64);
  --input: oklch(0.22 0.02 74.64);
  --ring: oklch(0.58 0.15 144.17);

  --chart-1: oklch(0.72 0.16 144.21);
  --chart-2: oklch(0.63 0.14 144.18);
  --chart-3: oklch(0.58 0.15 144.17);
  --chart-4: oklch(0.48 0.12 144.31);
  --chart-5: oklch(0.35 0.08 145.73);

  --sidebar: oklch(0.15 0.01 74.42);
  --sidebar-foreground: oklch(0.95 0.02 80.72);
  --sidebar-primary: oklch(0.58 0.15 144.17);
  --sidebar-primary-foreground: oklch(0.09 0.01 80.72);
  --sidebar-accent: oklch(0.22 0.04 146.04);
  --sidebar-accent-foreground: oklch(0.65 0.12 144.31);
  --sidebar-border: oklch(0.22 0.02 74.64);
  --sidebar-ring: oklch(0.58 0.15 144.17);

  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px
    hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px
    hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}
