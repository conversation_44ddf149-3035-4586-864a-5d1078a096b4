"use client"

import { sampleUsageData } from "@/lib/dashboard-data"
import { DashboardBadge } from "./dashboard-badge"
import { DashboardButton } from "./dashboard-button" 
import { formatters } from "@/lib/dashboard-utils"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function DashboardDetailsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-50 sm:text-xl">
          Details
        </h1>
        <div className="flex items-center gap-2">
          <DashboardButton variant="secondary">
            Export Data
          </DashboardButton>
          <DashboardButton variant="primary">
            Add New Item
          </DashboardButton>
        </div>
      </div>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Owner</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Region</TableHead>
                  <TableHead>Costs</TableHead>
                  <TableHead>Stability</TableHead>
                  <TableHead>Last Edited</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sampleUsageData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {item.owner}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          item.status === 'live' 
                            ? 'default' 
                            : item.status === 'inactive' 
                            ? 'secondary' 
                            : 'destructive'
                        }
                      >
                        {item.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.region}</TableCell>
                    <TableCell>{formatters.currency(item.costs)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <div className="h-2 bg-gray-200 rounded-full dark:bg-gray-800">
                            <div 
                              className="h-2 bg-green-500 rounded-full transition-all duration-300"
                              style={{ width: `${item.stability}%` }}
                            />
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {item.stability}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {item.lastEdited}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <DashboardButton variant="ghost" className="h-8 px-2">
                          Edit
                        </DashboardButton>
                        <DashboardButton variant="ghost" className="h-8 px-2 text-red-600 hover:text-red-700">
                          Delete
                        </DashboardButton>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">
              {sampleUsageData.length}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total Items
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">
              {sampleUsageData.filter(item => item.status === 'live').length}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Live Items
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">
              {formatters.currency(
                sampleUsageData.reduce((acc, item) => acc + item.costs, 0)
              )}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total Costs
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold">
              {(sampleUsageData.reduce((acc, item) => acc + item.stability, 0) / sampleUsageData.length).toFixed(1)}%
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Avg Stability
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
