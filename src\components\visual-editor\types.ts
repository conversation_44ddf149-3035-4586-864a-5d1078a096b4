// Visual Editor Type Definitions

export interface DesignToken {
  name: string;
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'border' | 'shadow';
  category: string;
  cssVar: string;
}

export interface ComponentProperty {
  name: string;
  type: 'text' | 'color' | 'number' | 'select' | 'boolean' | 'spacing' | 'typography';
  value: any;
  options?: string[] | number[];
  min?: number;
  max?: number;
  step?: number;
  label: string;
  description?: string;
}

export interface ComponentVariant {
  name: string;
  props: Record<string, any>;
  label: string;
}

export interface EditableComponent {
  id: string;
  name: string;
  type: 'ui' | 'component' | 'block';
  title: string;
  description: string;
  component: React.ComponentType<any>;
  properties: ComponentProperty[];
  variants?: ComponentVariant[];
  designTokens: string[]; // CSS variables this component uses
  defaultProps: Record<string, any>;
}

export interface VisualEditorState {
  selectedComponent: EditableComponent | null;
  componentProps: Record<string, any>;
  designTokens: Record<string, string>;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  isDarkMode: boolean;
  isCodeViewOpen: boolean;
}

export interface PropertyControl {
  type: ComponentProperty['type'];
  component: React.ComponentType<PropertyControlProps>;
}

export interface PropertyControlProps {
  property: ComponentProperty;
  value: any;
  onChange: (value: any) => void;
}

export interface VisualEditorConfig {
  components: EditableComponent[];
  designTokens: DesignToken[];
  propertyControls: Record<string, PropertyControl>;
}
