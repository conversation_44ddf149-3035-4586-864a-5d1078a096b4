"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EditableComponentWrapper } from "@/components/demo-visual-editor/editable-component-wrapper";

function EditableDefaultInput() {
  return (
    <EditableComponentWrapper
      componentName="input"
      initialProps={{ placeholder: "Default", type: "text" }}
    >
      <Input placeholder="Default" />
    </EditableComponentWrapper>
  );
}

function EditableEmailInput() {
  return (
    <EditableComponentWrapper
      componentName="input"
      initialProps={{ placeholder: "Email", type: "email" }}
    >
      <Input type="email" placeholder="Email" />
    </EditableComponentWrapper>
  );
}

export const input = {
  name: "input",
  components: {
    Default: <EditableDefaultInput />,
    Email: <EditableEmailInput />,
    WithLabel: (
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label htmlFor="email">Label</Label>
        <Input type="email" id="email" placeholder="With label" />
      </div>
    ),
    WithButton: (
      <div className="flex w-full max-w-sm items-center space-x-2">
        <Input type="email" placeholder="With button" />
        <Button type="submit">Button</Button>
      </div>
    ),

    File: (
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label htmlFor="picture">File</Label>
        <Input id="picture" type="file" />
      </div>
    ),
  },
};
