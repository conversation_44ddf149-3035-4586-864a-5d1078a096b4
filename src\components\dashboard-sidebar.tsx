"use client"

import { cx, focusRing } from "@/lib/dashboard-utils"
import Link from "next/link"
import { usePathname } from "next/navigation"

// Using Lucide icons instead of RemixIcon for compatibility
import { Home, BarChart3, Settings, ExternalLink } from "lucide-react"

const navigation = [
  { name: "Overview", href: "/overview", icon: Home },
  { name: "Details", href: "/details", icon: BarChart3 },
  { name: "Settings", href: "/settings", icon: Settings },
] as const

const shortcuts = [
  {
    name: "Add new user",
    href: "/settings/users", 
    icon: ExternalLink,
  },
  {
    name: "Workspace usage",
    href: "/settings/billing#billing-overview",
    icon: ExternalLink,
  },
  {
    name: "Cost spend control", 
    href: "/settings/billing#cost-spend-control",
    icon: ExternalLink,
  },
  {
    name: "Overview – Rows written",
    href: "/overview#usage-overview",
    icon: ExternalLink,
  },
] as const

export function DashboardSidebar() {
  const pathname = usePathname()
  
  const isActive = (itemHref: string) => {
    if (itemHref === "/settings") {
      return pathname.startsWith("/settings")
    }
    return pathname === itemHref || pathname.startsWith(itemHref)
  }

  return (
    <nav className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
      <aside className="flex grow flex-col gap-y-6 overflow-y-auto border-r border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-950">
        {/* Workspace selector placeholder */}
        <div className="flex h-10 items-center justify-between rounded-md border border-gray-300 px-3 py-2 dark:border-gray-700">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            My Workspace
          </span>
        </div>
        
        <nav
          aria-label="core navigation links"
          className="flex flex-1 flex-col space-y-10"
        >
          <ul role="list" className="space-y-0.5">
            {navigation.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cx(
                    isActive(item.href)
                      ? "text-indigo-600 dark:text-indigo-400"
                      : "text-gray-700 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50",
                    "flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-sm font-medium transition hover:bg-gray-100 hover:dark:bg-gray-900",
                    focusRing,
                  )}
                >
                  <item.icon className="size-4 shrink-0" aria-hidden="true" />
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          
          <div>
            <span className="text-xs font-medium leading-6 text-gray-500">
              Shortcuts
            </span>
            <ul aria-label="shortcuts" role="list" className="space-y-0.5">
              {shortcuts.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cx(
                      pathname === item.href || pathname.startsWith(item.href)
                        ? "text-indigo-600 dark:text-indigo-400"
                        : "text-gray-700 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50",
                      "flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-sm font-medium transition hover:bg-gray-100 hover:dark:bg-gray-900",
                      focusRing,
                    )}
                  >
                    <item.icon
                      className="size-4 shrink-0"
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>
        
        {/* User profile placeholder */}
        <div className="mt-auto">
          <div className="flex items-center gap-x-3 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-900">
            <div className="h-8 w-8 rounded-full bg-indigo-600"></div>
            <div className="flex-1 text-sm">
              <p className="font-medium text-gray-900 dark:text-gray-100">
                John Doe
              </p>
              <p className="text-gray-500 dark:text-gray-400"><EMAIL></p>
            </div>
          </div>
        </div>
      </aside>
    </nav>
  )
}

// Mobile sidebar toggle component
export function MobileSidebarToggle() {
  return (
    <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center justify-between border-b border-gray-200 bg-white px-2 shadow-sm sm:gap-x-6 sm:px-4 lg:hidden dark:border-gray-800 dark:bg-gray-950">
      <div className="flex h-10 items-center justify-between rounded-md border border-gray-300 px-3 py-2 dark:border-gray-700">
        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
          My Workspace
        </span>
      </div>
      
      <div className="flex items-center gap-1 sm:gap-2">
        <div className="h-8 w-8 rounded-full bg-indigo-600"></div>
      </div>
    </div>
  )
}
