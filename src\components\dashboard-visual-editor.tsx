"use client"

import { VisualEditor } from "./visual-editor"
import { DashboardButton } from "./dashboard-button"
import { DashboardBadge } from "./dashboard-badge"
import { DashboardChartCard } from "./dashboard-chart-card"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function DashboardVisualEditor() {
  return (
    <VisualEditor componentName="DashboardButton">
      {/* Example Dashboard Components for Editing */}
      <div className="space-y-6">
        <div className="dashboard-card">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Sample Dashboard Card</span>
                <DashboardBadge variant="success">+12.5%</DashboardBadge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-2xl font-bold">$12,543</div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                
                <div className="flex gap-2">
                  <DashboardButton variant="primary" className="dashboard-button">
                    Primary Action
                  </DashboardButton>
                  <DashboardButton variant="secondary" className="dashboard-button">
                    Secondary
                  </DashboardButton>
                  <DashboardButton variant="ghost" className="dashboard-button">
                    Ghost
                  </DashboardButton>
                </div>
                
                <div className="flex gap-2">
                  <DashboardBadge variant="success">Success</DashboardBadge>
                  <DashboardBadge variant="warning">Warning</DashboardBadge>
                  <DashboardBadge variant="error">Error</DashboardBadge>
                  <DashboardBadge variant="neutral">Neutral</DashboardBadge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DashboardChartCard
            title="Active Users"
            value={1234}
            previousValue={1156}
            type="unit"
            className="dashboard-card"
          />
          <DashboardChartCard
            title="Revenue"
            value={25678}
            previousValue={23456}
            type="currency"
            className="dashboard-card"
          />
        </div>
        
        <Card className="dashboard-card">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-2">
              <DashboardButton variant="light" className="dashboard-button w-full">
                Export Data
              </DashboardButton>
              <DashboardButton variant="secondary" className="dashboard-button w-full">
                View Reports
              </DashboardButton>
              <DashboardButton variant="primary" className="dashboard-button w-full">
                Add User
              </DashboardButton>
              <DashboardButton variant="destructive" className="dashboard-button w-full">
                Delete Item
              </DashboardButton>
            </div>
          </CardContent>
        </Card>
      </div>
    </VisualEditor>
  )
}
