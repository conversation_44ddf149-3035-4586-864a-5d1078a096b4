"use client";

import { But<PERSON> } from "@/components/ui/button";
import { EditableComponentWrapper } from "@/components/demo-visual-editor/editable-component-wrapper";

// Editable Button Components
function EditablePrimaryButton() {
  return (
    <EditableComponentWrapper
      componentName="button"
      initialProps={{ variant: "default", children: "Primary" }}
    >
      <Button variant="default">Primary</Button>
    </EditableComponentWrapper>
  );
}

function EditableSecondaryButton() {
  return (
    <EditableComponentWrapper
      componentName="button"
      initialProps={{ variant: "secondary", children: "Secondary" }}
    >
      <Button variant="secondary">Secondary</Button>
    </EditableComponentWrapper>
  );
}

function EditableOutlineButton() {
  return (
    <EditableComponentWrapper
      componentName="button"
      initialProps={{ variant: "outline", children: "Outline" }}
    >
      <Button variant="outline">Outline</Button>
    </EditableComponentWrapper>
  );
}

export const button = {
  name: "button",
  components: {
    Primary: <EditablePrimaryButton />,
    Secondary: <EditableSecondaryButton />,
    Outline: <EditableOutlineButton />,
  },
};
