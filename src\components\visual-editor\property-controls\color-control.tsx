"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import type { PropertyControlProps } from "../types";

// Common color presets
const colorPresets = [
  '#000000', '#ffffff', '#ef4444', '#f97316', '#eab308', 
  '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899',
  '#64748b', '#6b7280', '#71717a', '#737373', '#a3a3a3',
];

export function ColorControl({ property, value, onChange }: PropertyControlProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value || '#000000');

  const handleColorChange = (color: string) => {
    setInputValue(color);
    onChange(color);
  };

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    // Validate hex color format
    if (/^#[0-9A-F]{6}$/i.test(newValue) || /^#[0-9A-F]{3}$/i.test(newValue)) {
      onChange(newValue);
    }
  };

  return (
    <div className="space-y-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start h-10 px-3"
          >
            <div
              className="w-6 h-6 rounded border mr-2 flex-shrink-0"
              style={{ backgroundColor: value || '#000000' }}
            />
            <span className="font-mono text-sm">{value || '#000000'}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-4" align="start">
          <div className="space-y-4">
            {/* Color Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Hex Color</label>
              <Input
                type="text"
                value={inputValue}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="#000000"
                className="font-mono"
              />
            </div>

            {/* Color Presets */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Presets</label>
              <div className="grid grid-cols-5 gap-2">
                {colorPresets.map((color) => (
                  <button
                    key={color}
                    className={cn(
                      "w-8 h-8 rounded border-2 transition-all",
                      value === color 
                        ? "border-primary ring-2 ring-primary/20" 
                        : "border-border hover:border-primary/50"
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>

            {/* Native Color Picker */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Color Picker</label>
              <input
                type="color"
                value={value || '#000000'}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-full h-10 rounded border cursor-pointer"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
