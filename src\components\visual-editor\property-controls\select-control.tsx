"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { PropertyControlProps } from "../types";

export function SelectControl({ property, value, onChange }: PropertyControlProps) {
  const options = property.options || [];

  return (
    <Select value={value?.toString()} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder={`Select ${property.label.toLowerCase()}...`} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.toString()} value={option.toString()}>
            {formatOptionLabel(option)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

function formatOptionLabel(option: string | number): string {
  if (typeof option === 'string') {
    // Convert kebab-case or camelCase to Title Case
    return option
      .replace(/[-_]/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, l => l.toUpperCase());
  }
  return option.toString();
}
