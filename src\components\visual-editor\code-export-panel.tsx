"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Copy, Check, Download } from "lucide-react";
import { cn } from "@/lib/utils";
import type { EditableComponent } from "./types";

interface CodeExportPanelProps {
  component: EditableComponent | null;
  props: Record<string, any>;
  designTokens: Record<string, string>;
}

export function CodeExportPanel({
  component,
  props,
  designTokens,
}: CodeExportPanelProps) {
  const [copiedSection, setCopiedSection] = useState<string | null>(null);

  if (!component) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center space-y-2">
          <div className="text-muted-foreground">No Component Selected</div>
          <p className="text-sm text-muted-foreground">
            Select a component to view its code
          </p>
        </div>
      </div>
    );
  }

  const generateCode = () => {
    // Generate component JSX
    const propsString = Object.entries(props)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `  ${key}="${value}"`;
        }
        if (typeof value === 'boolean') {
          return value ? `  ${key}` : '';
        }
        if (typeof value === 'number') {
          return `  ${key}={${value}}`;
        }
        return `  ${key}={${JSON.stringify(value)}}`;
      })
      .filter(Boolean)
      .join('\n');

    const componentCode = `<${component.name}${propsString ? `\n${propsString}` : ''}\n/>`;

    // Generate imports
    const imports = `import { ${component.name} } from "@/components/${component.name.toLowerCase()}";`;

    // Generate CSS tokens (only modified ones)
    const modifiedTokens = Object.entries(designTokens)
      .filter(([token, value]) => {
        // Check if this token was actually modified from default
        const defaultValue = getComputedStyle(document.documentElement)
          .getPropertyValue(token)
          .trim();
        return value !== defaultValue;
      });

    const tokenCode = modifiedTokens.length > 0 
      ? `:root {\n${modifiedTokens.map(([token, value]) => `  ${token}: ${value};`).join('\n')}\n}`
      : '';

    // Generate full page code
    const fullPageCode = `${imports}

export default function Page() {
  return (
    <div className="min-h-screen p-8">
      ${componentCode}
    </div>
  );
}`;

    return {
      component: componentCode,
      imports,
      tokens: tokenCode,
      fullPage: fullPageCode,
    };
  };

  const code = generateCode();

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const downloadCode = () => {
    const blob = new Blob([code.fullPage], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${component.name.toLowerCase()}-component.tsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-lg">Code Export</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={downloadCode}
            className="h-8"
          >
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <span className="font-medium text-sm">{component.title}</span>
          <Badge variant="outline" className="text-xs">
            {component.type}
          </Badge>
        </div>
      </div>

      <div className="flex-1">
        <Tabs defaultValue="component" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
            <TabsTrigger value="component" className="text-xs">Component</TabsTrigger>
            <TabsTrigger value="imports" className="text-xs">Imports</TabsTrigger>
            <TabsTrigger value="tokens" className="text-xs">Tokens</TabsTrigger>
            <TabsTrigger value="full" className="text-xs">Full Page</TabsTrigger>
          </TabsList>

          <div className="flex-1 p-4">
            <TabsContent value="component" className="h-full mt-0">
              <CodeBlock
                code={code.component}
                language="jsx"
                title="Component JSX"
                onCopy={() => copyToClipboard(code.component, 'component')}
                copied={copiedSection === 'component'}
              />
            </TabsContent>

            <TabsContent value="imports" className="h-full mt-0">
              <CodeBlock
                code={code.imports}
                language="javascript"
                title="Import Statement"
                onCopy={() => copyToClipboard(code.imports, 'imports')}
                copied={copiedSection === 'imports'}
              />
            </TabsContent>

            <TabsContent value="tokens" className="h-full mt-0">
              <CodeBlock
                code={code.tokens || '/* No modified tokens */'}
                language="css"
                title="CSS Tokens"
                onCopy={() => copyToClipboard(code.tokens, 'tokens')}
                copied={copiedSection === 'tokens'}
                disabled={!code.tokens}
              />
            </TabsContent>

            <TabsContent value="full" className="h-full mt-0">
              <CodeBlock
                code={code.fullPage}
                language="tsx"
                title="Complete Page"
                onCopy={() => copyToClipboard(code.fullPage, 'full')}
                copied={copiedSection === 'full'}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}

interface CodeBlockProps {
  code: string;
  language: string;
  title: string;
  onCopy: () => void;
  copied: boolean;
  disabled?: boolean;
}

function CodeBlock({ code, language, title, onCopy, copied, disabled }: CodeBlockProps) {
  return (
    <div className="h-full flex flex-col border rounded-md">
      <div className="flex items-center justify-between p-3 border-b bg-muted/50">
        <div className="flex items-center space-x-2">
          <span className="font-medium text-sm">{title}</span>
          <Badge variant="outline" className="text-xs">
            {language}
          </Badge>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onCopy}
          disabled={disabled}
          className="h-7"
        >
          {copied ? (
            <Check className="h-3 w-3 mr-1" />
          ) : (
            <Copy className="h-3 w-3 mr-1" />
          )}
          {copied ? 'Copied' : 'Copy'}
        </Button>
      </div>
      
      <ScrollArea className="flex-1">
        <pre className={cn(
          "p-4 text-sm font-mono leading-relaxed",
          disabled && "text-muted-foreground"
        )}>
          <code>{code}</code>
        </pre>
      </ScrollArea>
    </div>
  );
}
