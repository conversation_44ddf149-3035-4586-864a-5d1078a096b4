"use client"

import { useState } from "react"
import { DashboardSidebar, MobileSidebarToggle } from "./dashboard-sidebar"
import { DashboardOverviewPage } from "./dashboard-overview-page"
import { DashboardDetailsPage } from "./dashboard-details-page"
import { DashboardSettingsGeneralPage } from "./dashboard-settings-general-page"
import { DashboardSettingsUsersPage } from "./dashboard-settings-users-page"
import { DashboardSettingsBillingPage } from "./dashboard-settings-billing-page"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function MultiPageDashboard() {
  const [currentPage, setCurrentPage] = useState("overview")

  const renderPage = () => {
    switch (currentPage) {
      case "overview":
        return <DashboardOverviewPage />
      case "details":
        return <DashboardDetailsPage />
      case "settings-general":
        return <DashboardSettingsGeneralPage />
      case "settings-users":
        return <DashboardSettingsUsersPage />
      case "settings-billing":
        return <DashboardSettingsBillingPage />
      default:
        return <DashboardOverviewPage />
    }
  }

  const isSettingsPage = currentPage.startsWith("settings")

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Enhanced Sidebar with Navigation */}
      <nav className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <aside className="flex grow flex-col gap-y-6 overflow-y-auto border-r border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-950">
          {/* Workspace selector */}
          <div className="flex h-10 items-center justify-between rounded-md border border-gray-300 px-3 py-2 dark:border-gray-700">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              My Workspace
            </span>
          </div>
          
          <nav className="flex flex-1 flex-col space-y-10">
            <ul role="list" className="space-y-0.5">
              <li>
                <button
                  onClick={() => setCurrentPage("overview")}
                  className={`w-full flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-sm font-medium transition hover:bg-gray-100 hover:dark:bg-gray-900 ${
                    currentPage === "overview"
                      ? "text-indigo-600 dark:text-indigo-400"
                      : "text-gray-700 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50"
                  }`}
                >
                  <span className="size-4">📊</span>
                  Overview
                </button>
              </li>
              <li>
                <button
                  onClick={() => setCurrentPage("details")}
                  className={`w-full flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-sm font-medium transition hover:bg-gray-100 hover:dark:bg-gray-900 ${
                    currentPage === "details"
                      ? "text-indigo-600 dark:text-indigo-400"
                      : "text-gray-700 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50"
                  }`}
                >
                  <span className="size-4">📋</span>
                  Details
                </button>
              </li>
              <li>
                <button
                  onClick={() => setCurrentPage("settings-general")}
                  className={`w-full flex items-center gap-x-2.5 rounded-md px-2 py-1.5 text-sm font-medium transition hover:bg-gray-100 hover:dark:bg-gray-900 ${
                    isSettingsPage
                      ? "text-indigo-600 dark:text-indigo-400"
                      : "text-gray-700 hover:text-gray-900 dark:text-gray-400 hover:dark:text-gray-50"
                  }`}
                >
                  <span className="size-4">⚙️</span>
                  Settings
                </button>
              </li>
            </ul>
          </nav>
          
          {/* User profile */}
          <div className="mt-auto">
            <div className="flex items-center gap-x-3 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-900">
              <div className="h-8 w-8 rounded-full bg-indigo-600"></div>
              <div className="flex-1 text-sm">
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  John Doe
                </p>
                <p className="text-gray-500 dark:text-gray-400"><EMAIL></p>
              </div>
            </div>
          </div>
        </aside>
      </nav>

      {/* Mobile header */}
      <MobileSidebarToggle />
      
      {/* Main content */}
      <div className="lg:pl-72">
        <div className="p-4 sm:px-6 sm:pb-10 sm:pt-10 lg:px-10 lg:pt-7">
          {isSettingsPage ? (
            <div>
              <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-50">
                  Settings
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Manage your workspace settings and preferences.
                </p>
              </div>
              
              <Tabs value={currentPage} onValueChange={setCurrentPage}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="settings-general">General</TabsTrigger>
                  <TabsTrigger value="settings-users">Users</TabsTrigger>
                  <TabsTrigger value="settings-billing">Billing</TabsTrigger>
                </TabsList>
                
                <div className="mt-6">
                  <TabsContent value="settings-general">
                    <DashboardSettingsGeneralPage />
                  </TabsContent>
                  <TabsContent value="settings-users">
                    <DashboardSettingsUsersPage />
                  </TabsContent>
                  <TabsContent value="settings-billing">
                    <DashboardSettingsBillingPage />
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          ) : (
            renderPage()
          )}
        </div>
      </div>
    </div>
  )
}
