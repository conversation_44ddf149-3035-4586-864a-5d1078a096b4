"use client";

import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";
import type { EditableComponent, ComponentProperty, PropertyControl } from "./types";
// PropertyControls are handled via the propertyControls prop

interface PropertyPanelProps {
  component: EditableComponent;
  values: Record<string, any>;
  onChange: (propName: string, value: any) => void;
  propertyControls: Record<string, PropertyControl>;
}

export function PropertyPanel({
  component,
  values,
  onChange,
  propertyControls,
}: PropertyPanelProps) {
  const [openSections, setOpenSections] = useState<Set<string>>(
    new Set(['content', 'appearance', 'layout', 'behavior'])
  );

  const toggleSection = (section: string) => {
    const newOpenSections = new Set(openSections);
    if (newOpenSections.has(section)) {
      newOpenSections.delete(section);
    } else {
      newOpenSections.add(section);
    }
    setOpenSections(newOpenSections);
  };

  // Group properties by category
  const groupedProperties = component.properties.reduce((acc, property) => {
    const category = getPropertyCategory(property);
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(property);
    return acc;
  }, {} as Record<string, ComponentProperty[]>);

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg">{component.title}</h3>
            <Badge variant="outline" className="text-xs">
              {component.type}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {component.description}
          </p>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Component Variants */}
          {component.variants && component.variants.length > 0 && (
            <PropertySection
              title="Variants"
              isOpen={openSections.has('variants')}
              onToggle={() => toggleSection('variants')}
            >
              <div className="space-y-2">
                {component.variants.map((variant) => (
                  <button
                    key={variant.name}
                    className={cn(
                      "w-full p-2 text-left rounded-md border transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      // Check if current props match this variant
                      JSON.stringify(values) === JSON.stringify(variant.props)
                        ? "bg-primary text-primary-foreground"
                        : "bg-background"
                    )}
                    onClick={() => {
                      Object.entries(variant.props).forEach(([key, value]) => {
                        onChange(key, value);
                      });
                    }}
                  >
                    <div className="font-medium text-sm">{variant.label}</div>
                  </button>
                ))}
              </div>
            </PropertySection>
          )}

          {/* Property Groups */}
          {Object.entries(groupedProperties).map(([category, properties]) => (
            <PropertySection
              key={category}
              title={getCategoryLabel(category)}
              isOpen={openSections.has(category)}
              onToggle={() => toggleSection(category)}
            >
              <div className="space-y-4">
                {properties.map((property) => {
                  const ControlComponent = propertyControls[property.type]?.component;
                  if (!ControlComponent) {
                    return null;
                  }

                  return (
                    <div key={property.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium">
                          {property.label}
                        </label>
                        {property.description && (
                          <div className="text-xs text-muted-foreground">
                            ?
                          </div>
                        )}
                      </div>
                      <ControlComponent
                        property={property}
                        value={values[property.name] ?? property.value}
                        onChange={(value) => onChange(property.name, value)}
                      />
                    </div>
                  );
                })}
              </div>
            </PropertySection>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface PropertySectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

function PropertySection({ title, isOpen, onToggle, children }: PropertySectionProps) {
  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-accent rounded-md">
        <span className="font-medium text-sm">{title}</span>
        {isOpen ? (
          <ChevronDown className="size-4" />
        ) : (
          <ChevronRight className="size-4" />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="mt-2">
        {children}
      </CollapsibleContent>
      <Separator className="my-4" />
    </Collapsible>
  );
}

function getPropertyCategory(property: ComponentProperty): string {
  // Categorize properties based on their type and name
  if (property.type === 'color') return 'appearance';
  if (property.type === 'spacing') return 'layout';
  if (property.type === 'typography') return 'appearance';
  if (property.name.includes('text') || property.name.includes('title') || property.name.includes('description')) {
    return 'content';
  }
  if (property.name.includes('size') || property.name.includes('width') || property.name.includes('height')) {
    return 'layout';
  }
  if (property.type === 'boolean') return 'behavior';
  
  return 'content';
}

function getCategoryLabel(category: string): string {
  const labels: Record<string, string> = {
    content: 'Content',
    appearance: 'Appearance',
    layout: 'Layout',
    behavior: 'Behavior',
    variants: 'Variants',
  };
  return labels[category] || category;
}
