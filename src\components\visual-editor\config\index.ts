"use client";

import { editableComponents } from "./editable-components";
import { designTokens } from "./design-tokens";
import { propertyControls } from "../property-controls";
import type { VisualEditorConfig } from "../types";

export const visualEditorConfig: VisualEditorConfig = {
  components: editableComponents,
  designTokens: designTokens,
  propertyControls: propertyControls,
};

export { editableComponents, designTokens };
export * from "./editable-components";
export * from "./design-tokens";
