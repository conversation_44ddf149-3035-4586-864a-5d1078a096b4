"use client";

import { useState } from "react";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { ComponentSelector } from "./component-selector";
import { PropertyPanel } from "./property-panel";
import { PreviewPanel } from "./preview-panel";
import { CodeExportPanel } from "./code-export-panel";
import { DesignTokenPanel } from "./design-token-panel";
import { Toolbar } from "./toolbar";
import { useVisualEditor } from "./hooks/use-visual-editor";
import type { VisualEditorConfig } from "./types";

interface VisualEditorProps {
  config: VisualEditorConfig;
}

export function VisualEditor({ config }: VisualEditorProps) {
  const {
    state,
    actions: {
      selectComponent,
      updateComponentProp,
      updateDesignToken,
      setPreviewMode,
      toggleDarkMode,
      toggleCodeView,
      resetComponent,
      exportCode,
    },
  } = useVisualEditor(config);

  const [leftPanelSize, setLeftPanelSize] = useState(25);
  const [rightPanelSize, setRightPanelSize] = useState(25);

  return (
    <div className="h-screen w-full bg-background">
      {/* Top Toolbar */}
      <Toolbar
        previewMode={state.previewMode}
        isDarkMode={state.isDarkMode}
        isCodeViewOpen={state.isCodeViewOpen}
        onPreviewModeChange={setPreviewMode}
        onToggleDarkMode={toggleDarkMode}
        onToggleCodeView={toggleCodeView}
        onReset={resetComponent}
        onExport={exportCode}
        selectedComponent={state.selectedComponent}
      />

      {/* Main Editor Layout */}
      <div className="h-[calc(100vh-64px)]">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Component Selection & Properties */}
          <ResizablePanel 
            defaultSize={leftPanelSize} 
            minSize={20} 
            maxSize={40}
            className="border-r"
          >
            <div className="h-full flex flex-col">
              {/* Component Selector */}
              <div className="border-b p-4">
                <ComponentSelector
                  components={config.components}
                  selectedComponent={state.selectedComponent}
                  onSelectComponent={selectComponent}
                />
              </div>

              {/* Property Panel */}
              <div className="flex-1 overflow-auto">
                {state.selectedComponent && (
                  <PropertyPanel
                    component={state.selectedComponent}
                    values={state.componentProps}
                    onChange={updateComponentProp}
                    propertyControls={config.propertyControls}
                  />
                )}
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle />

          {/* Center Panel - Preview */}
          <ResizablePanel defaultSize={50} minSize={30}>
            <PreviewPanel
              component={state.selectedComponent}
              props={state.componentProps}
              designTokens={state.designTokens}
              previewMode={state.previewMode}
              isDarkMode={state.isDarkMode}
            />
          </ResizablePanel>

          <ResizableHandle />

          {/* Right Panel - Design Tokens & Code Export */}
          <ResizablePanel 
            defaultSize={rightPanelSize} 
            minSize={20} 
            maxSize={40}
            className="border-l"
          >
            <div className="h-full flex flex-col">
              {state.isCodeViewOpen ? (
                <CodeExportPanel
                  component={state.selectedComponent}
                  props={state.componentProps}
                  designTokens={state.designTokens}
                />
              ) : (
                <DesignTokenPanel
                  tokens={config.designTokens}
                  values={state.designTokens}
                  onChange={updateDesignToken}
                  selectedComponent={state.selectedComponent}
                />
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
