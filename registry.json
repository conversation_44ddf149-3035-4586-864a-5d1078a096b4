{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "Registry Starter", "homepage": "https://registry-starter.vercel.app", "items": [{"name": "registry", "type": "registry:style", "cssVars": {"light": {"primary": "oklch(0.52 0.13 144.17)", "primary-foreground": "oklch(1.0 0 0)", "radius": "0.5rem"}, "dark": {"primary": "oklch(0.52 0.13 144.17)", "primary-foreground": "oklch(1.0 0 0)"}}, "files": []}, {"name": "theme", "type": "registry:theme", "title": "Global Theme", "description": "Brand themed styles using Tailwind and Shadcn/ui", "files": [{"path": "src/app/tokens.css", "type": "registry:file", "target": "app/tokens.css"}, {"path": "src/v0/globals.css", "type": "registry:file", "target": "app/globals.css"}, {"path": "src/v0/tailwind.config.ts", "type": "registry:file", "target": "tailwind.config.ts"}]}, {"name": "blank", "type": "registry:block", "title": "Blank", "description": "A blank application with all brand components and code", "registryDependencies": ["https://registry-starter.vercel.app/r/brand-header.json", "https://registry-starter.vercel.app/r/brand-sidebar.json", "https://registry-starter.vercel.app/r/product-grid.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dashboard", "type": "registry:block", "title": "Dashboard", "description": "A dashboard application with your brand themed components", "registryDependencies": ["https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/brand-header.json", "https://registry-starter.vercel.app/r/brand-sidebar.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/dashboard-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "store", "type": "registry:block", "title": "Store", "description": "A store application with your brand themed components", "registryDependencies": ["https://registry-starter.vercel.app/r/product-grid.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/store-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "brand-header", "type": "registry:component", "title": "<PERSON>er", "description": "A styled, simple, reusable header", "registryDependencies": ["button", "input", "avatar", "sidebar", "https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/logo.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/brand-header.tsx", "type": "registry:component"}, {"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "brand-sidebar", "type": "registry:component", "title": "Brand Sidebar", "description": "A styled, simple, reusable sidebar", "registryDependencies": ["badge", "button", "sidebar", "https://registry-starter.vercel.app/r/sonner.json", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/brand-sidebar.tsx", "type": "registry:component"}, {"path": "src/v0/shell-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "login", "type": "registry:component", "title": "<PERSON><PERSON>", "description": "Username & password login section with customer quote.", "registryDependencies": ["badge", "button", "https://registry-starter.vercel.app/r/theme.json", "https://registry-starter.vercel.app/r/logo.json"], "files": [{"path": "src/components/login.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "logo", "type": "registry:component", "title": "Brand Logo", "description": "A styled, simple, reusable logo", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/logo.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "hero", "type": "registry:component", "title": "Hero", "description": "Attention-grabbing section for the top of your landing pages.", "registryDependencies": ["badge", "button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/hero.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "promo", "type": "registry:component", "title": "Promo", "description": "Attention-grabbing section to display the current promotional deal.", "registryDependencies": ["button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/promo.tsx", "type": "registry:component"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/app/demo/[name]/blocks/blank-page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "product-grid", "type": "registry:component", "title": "Product Grid", "description": "Product grid displaying all products with API to fetch data", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/product-grid.tsx", "type": "registry:component"}, {"path": "src/lib/products.ts", "type": "registry:lib"}, {"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "complete-dashboard", "type": "registry:block", "title": "Complete Dashboard", "description": "A complete dashboard layout with sidebar, metrics cards, and sample data", "registryDependencies": ["card", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/complete-dashboard.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-sidebar.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-chart-card.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-schema.ts", "type": "registry:lib"}, {"path": "src/app/demo/complete-dashboard/page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dashboard-button", "type": "registry:component", "title": "Dashboard Button", "description": "Tremor Raw styled button component with loading states", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}]}, {"name": "dashboard-badge", "type": "registry:component", "title": "Dashboard Badge", "description": "Tremor Raw styled badge component with status variants", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}]}, {"name": "dashboard-sidebar", "type": "registry:component", "title": "Dashboard Sidebar", "description": "Responsive sidebar navigation with desktop and mobile versions", "registryDependencies": ["https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-sidebar.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}]}, {"name": "dashboard-chart-card", "type": "registry:component", "title": "Dashboard Chart Card", "description": "Metric display card with chart visualization and comparison data", "registryDependencies": ["card", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-chart-card.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}]}, {"name": "multi-page-dashboard", "type": "registry:block", "title": "Multi-Page Dashboard", "description": "Complete multi-page dashboard with Overview, Details, and Settings pages with routing", "registryDependencies": ["card", "tabs", "table", "progress", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/multi-page-dashboard.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-overview-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-details-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-settings-general-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-settings-users-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-settings-billing-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-sidebar.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-chart-card.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-schema.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-data.ts", "type": "registry:lib"}, {"path": "src/app/demo/multi-page-dashboard/page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dashboard-overview-page", "type": "registry:component", "title": "Dashboard Overview Page", "description": "Complete overview dashboard page with KPI cards and charts", "registryDependencies": ["card", "progress", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-overview-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-chart-card.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-schema.ts", "type": "registry:lib"}, {"path": "src/app/demo/dashboard-overview/page.tsx", "type": "registry:page", "target": "app/page.tsx"}]}, {"name": "dashboard-details-page", "type": "registry:component", "title": "Dashboard Details Page", "description": "Data table page with usage details and summary statistics", "registryDependencies": ["card", "table", "badge", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-details-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-badge.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-data.ts", "type": "registry:lib"}]}, {"name": "dashboard-settings-pages", "type": "registry:component", "title": "Dashboard Settings Pages", "description": "Complete settings pages including General, Users, and Billing", "registryDependencies": ["card", "input", "select", "checkbox", "switch", "separator", "dropdown-menu", "progress", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/dashboard-settings-general-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-settings-users-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-settings-billing-page.tsx", "type": "registry:component"}, {"path": "src/components/dashboard-button.tsx", "type": "registry:component"}, {"path": "src/lib/dashboard-utils.ts", "type": "registry:lib"}, {"path": "src/lib/dashboard-data.ts", "type": "registry:lib"}]}, {"name": "visual-editor", "type": "registry:component", "title": "Visual Component Editor", "description": "Figma-like visual editor for customizing components with color pickers, text inputs, and live preview", "registryDependencies": ["card", "tabs", "input", "select", "badge", "button", "separator", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/components/visual-editor.tsx", "type": "registry:component"}, {"path": "src/components/visual-editor-with-preview.tsx", "type": "registry:component"}, {"path": "src/components/enhanced-component-page.tsx", "type": "registry:component"}, {"path": "src/app/demo/visual-editor/page.tsx", "type": "registry:page", "target": "app/page.tsx"}, {"path": "src/app/editor/[component]/page.tsx", "type": "registry:page"}]}, {"name": "accordion", "type": "registry:ui", "title": "Accordion", "description": "A vertically stacked set of interactive headings that each reveal a section of content.", "registryDependencies": ["accordion", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "alert", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Displays a callout for user attention.", "registryDependencies": ["alert", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "avatar", "type": "registry:ui", "title": "Avatar", "description": "An image element with a fallback for representing the user.", "registryDependencies": ["avatar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "badge", "type": "registry:ui", "title": "Badge", "description": "Displays a small count or status indicator.", "registryDependencies": ["badge", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "breadcrumb", "type": "registry:ui", "title": "Breadcrumb", "description": "Displays the path to the current resource using a hierarchy of links.", "registryDependencies": ["breadcrumb", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "button", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "Allows users to take actions with a single click or tap.", "registryDependencies": ["button", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "calendar", "type": "registry:ui", "title": "Calendar", "description": "A date field component that allows users to enter and edit date.", "registryDependencies": ["calendar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "card", "type": "registry:ui", "title": "Card", "description": "Containers for displaying content and actions about a single subject.", "registryDependencies": ["card", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "chart", "type": "registry:ui", "title": "Chart", "description": "Beautiful charts. Built using Recharts. Copy and paste into your apps.", "registryDependencies": ["chart", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "checkbox", "type": "registry:ui", "title": "Checkbox", "description": "Allows users to select multiple items from a list of options.", "registryDependencies": ["checkbox", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "data-table", "type": "registry:ui", "title": "Data Table", "description": "Powerful table and datagrids built using TanStack Table.", "registryDependencies": ["data-table", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "date-picker", "type": "registry:ui", "title": "Date Picker", "description": "Displays the path to the current resource using a hierarchy of links.", "registryDependencies": ["date-picker", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "dialog", "type": "registry:ui", "title": "Dialog", "description": "A modal dialog that interrupts the user with important content.", "registryDependencies": ["dialog", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "dropdown-menu", "type": "registry:ui", "title": "Dropdown", "description": "Displays a menu to the user triggered by a button.", "registryDependencies": ["dropdown-menu", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "input", "type": "registry:ui", "title": "Input", "description": "Displays a form input field or a component that looks like an input field.", "registryDependencies": ["input", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "menu-bar", "type": "registry:ui", "title": "Menu Bar", "description": "A visually persistent menu common in desktop applications that provides quick access to a consistent set of commands.", "registryDependencies": ["menu-bar", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "select", "type": "registry:ui", "title": "Select", "description": "Displays a list of options for the user to pick from—triggered by a button.", "registryDependencies": ["select", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "separator", "type": "registry:ui", "title": "Separator", "description": "Visually or semantically separates content.", "registryDependencies": ["separator", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "skeleton", "type": "registry:ui", "title": "Skeleton", "description": "Use to show a placeholder while content is loading.", "registryDependencies": ["skeleton", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "slider", "type": "registry:ui", "title": "Slide<PERSON>", "description": "An input where the user selects a value from within a given range.", "registryDependencies": ["slider", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "sonner", "type": "registry:ui", "title": "<PERSON><PERSON>", "description": "An opinionated toast component for React.", "registryDependencies": ["sonner", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}, {"path": "src/components/ui/sonner.tsx", "type": "registry:file", "target": "components/ui/sonner.tsx"}]}, {"name": "switch", "type": "registry:ui", "title": "Switch", "description": "A control that allows the user to toggle between checked and not checked.", "registryDependencies": ["switch", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "table", "type": "registry:ui", "title": "Table", "description": "A responsive table component.", "registryDependencies": ["table", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "tabs", "type": "registry:ui", "title": "Tabs", "description": "A set of layered sections of content—known as tab panels—that are displayed one at a time.", "registryDependencies": ["tabs", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "toggle-group", "type": "registry:ui", "title": "Toggle Group", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "registryDependencies": ["toggle-group", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}, {"name": "tooltip", "type": "registry:ui", "title": "<PERSON><PERSON><PERSON>", "description": "A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.", "registryDependencies": ["tooltip", "https://registry-starter.vercel.app/r/theme.json"], "files": [{"path": "src/v0/minimal-layout.tsx", "type": "registry:file", "target": "app/layout.tsx"}]}]}