"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Bold, Italic, Underline } from "lucide-react";
import { cn } from "@/lib/utils";
import type { PropertyControlProps } from "../types";

interface TypographyValue {
  fontSize?: number;
  fontWeight?: string;
  fontFamily?: string;
  lineHeight?: number;
  letterSpacing?: number;
  textDecoration?: string;
  fontStyle?: string;
}

const fontFamilies = [
  'Inter',
  'Roboto',
  'Open Sans',
  'Lato',
  'Montserrat',
  'Source Sans Pro',
  'Poppins',
  'Nunito',
  'Arial',
  'Helvetica',
  'Georgia',
  'Times New Roman',
];

const fontWeights = [
  { value: '100', label: 'Thin' },
  { value: '200', label: 'Extra Light' },
  { value: '300', label: 'Light' },
  { value: '400', label: 'Regular' },
  { value: '500', label: 'Medium' },
  { value: '600', label: 'Semi Bold' },
  { value: '700', label: 'Bold' },
  { value: '800', label: 'Extra Bold' },
  { value: '900', label: 'Black' },
];

export function TypographyControl({ property, value, onChange }: PropertyControlProps) {
  const parseTypographyValue = (val: any): TypographyValue => {
    if (typeof val === 'object' && val !== null) {
      return val;
    }
    return {};
  };

  const typographyValue = parseTypographyValue(value);

  const updateTypography = (updates: Partial<TypographyValue>) => {
    onChange({
      ...typographyValue,
      ...updates,
    });
  };

  return (
    <div className="space-y-4">
      {/* Font Family */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Font Family</label>
        <Select
          value={typographyValue.fontFamily || 'Inter'}
          onValueChange={(value) => updateTypography({ fontFamily: value })}
        >
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {fontFamilies.map((font) => (
              <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                {font}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Font Size */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Font Size</label>
        <Slider
          value={[typographyValue.fontSize || 16]}
          onValueChange={([value]) => updateTypography({ fontSize: value })}
          min={8}
          max={72}
          step={1}
          className="w-full"
        />
        <div className="text-xs text-muted-foreground text-center">
          {typographyValue.fontSize || 16}px
        </div>
      </div>

      {/* Font Weight */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Font Weight</label>
        <Select
          value={typographyValue.fontWeight || '400'}
          onValueChange={(value) => updateTypography({ fontWeight: value })}
        >
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {fontWeights.map((weight) => (
              <SelectItem key={weight.value} value={weight.value}>
                {weight.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Text Style Toggles */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Style</label>
        <div className="flex space-x-1">
          <Button
            variant={typographyValue.fontWeight === '700' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => updateTypography({ 
              fontWeight: typographyValue.fontWeight === '700' ? '400' : '700' 
            })}
          >
            <Bold className="h-3 w-3" />
          </Button>
          <Button
            variant={typographyValue.fontStyle === 'italic' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => updateTypography({ 
              fontStyle: typographyValue.fontStyle === 'italic' ? 'normal' : 'italic' 
            })}
          >
            <Italic className="h-3 w-3" />
          </Button>
          <Button
            variant={typographyValue.textDecoration === 'underline' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => updateTypography({ 
              textDecoration: typographyValue.textDecoration === 'underline' ? 'none' : 'underline' 
            })}
          >
            <Underline className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Line Height */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Line Height</label>
        <Slider
          value={[typographyValue.lineHeight || 1.5]}
          onValueChange={([value]) => updateTypography({ lineHeight: value })}
          min={1}
          max={3}
          step={0.1}
          className="w-full"
        />
        <div className="text-xs text-muted-foreground text-center">
          {(typographyValue.lineHeight || 1.5).toFixed(1)}
        </div>
      </div>

      {/* Letter Spacing */}
      <div className="space-y-2">
        <label className="text-xs font-medium text-muted-foreground">Letter Spacing</label>
        <Slider
          value={[typographyValue.letterSpacing || 0]}
          onValueChange={([value]) => updateTypography({ letterSpacing: value })}
          min={-2}
          max={4}
          step={0.1}
          className="w-full"
        />
        <div className="text-xs text-muted-foreground text-center">
          {(typographyValue.letterSpacing || 0).toFixed(1)}px
        </div>
      </div>
    </div>
  );
}
