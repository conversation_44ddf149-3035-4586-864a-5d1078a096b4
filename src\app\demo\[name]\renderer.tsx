"use client";

import { type ReactNode, useEffect, cloneElement, isValidElement } from "react";
import { VisualEditingOverlay } from "@/components/demo-visual-editor/visual-editing-overlay";
import { componentConfigs } from "@/components/demo-visual-editor/component-configs";

interface RendererProps {
  children: ReactNode;
  componentName?: string;
}

export function Renderer({ children, componentName }: RendererProps) {
  useEffect(() => {
    const setTheme = (theme: string | null) => {
      if (theme === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    };

    // set initial theme on mount
    setTheme(localStorage.getItem("theme"));

    // listen for theme changes from parent
    function onStorage(e: StorageEvent) {
      if (e.key === "theme") {
        setTheme(e.newValue);
      }
    }

    window.addEventListener("storage", onStorage);
    return () => window.removeEventListener("storage", onStorage);
  }, []);

  // If we have a component name and config, wrap with visual editing
  if (componentName && componentConfigs[componentName]) {
    return (
      <VisualEditingOverlay
        componentName={componentName}
        componentConfig={componentConfigs[componentName]}
        onPropsChange={(newProps) => {
          // Handle props changes - this could update the component
          console.log('Props changed for', componentName, newProps);
        }}
      >
        {children}
      </VisualEditingOverlay>
    );
  }

  return <>{children}</>;
}
