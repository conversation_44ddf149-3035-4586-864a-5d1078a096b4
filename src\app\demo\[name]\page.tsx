import { notFound } from "next/navigation";

import { demos } from "@/app/demo/[name]/index";

import { Renderer } from "@/app/demo/[name]/renderer";
import { getRegistryItem } from "@/lib/registry";

export async function generateStaticParams() {
  return Object.keys(demos).map((name) => ({
    name,
  }));
}

export default async function DemoPage({
  params,
}: {
  params: Promise<{ name: string }>;
}) {
  const { name } = await params;

  const component = getRegistryItem(name);

  if (!component) {
    notFound();
  }

  const demo = demos[name];

  if (!demo) {
    return (
      <div className="flex h-[100vh] w-full flex-col items-center justify-center gap-4 bg-card">
        <div className="text-muted-foreground">
          Demo not available for "{name}"
        </div>
      </div>
    );
  }

  const { components } = demo;

  return (
    <div className="flex h-[100vh] w-full flex-col gap-4 bg-card">
      {components &&
        Object.entries(components).map(([key, node]) => (
          <div className="relative w-full" key={key}>
            <Renderer componentName={name}>{node}</Renderer>
          </div>
        ))}
    </div>
  );
}
