"use client";

import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import type { EditableComponent } from "./types";

interface PreviewPanelProps {
  component: EditableComponent | null;
  props: Record<string, any>;
  designTokens: Record<string, string>;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  isDarkMode: boolean;
}

export function PreviewPanel({
  component,
  props,
  designTokens,
  previewMode,
  isDarkMode,
}: PreviewPanelProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Apply design tokens to the preview
  useEffect(() => {
    if (iframeRef.current?.contentDocument) {
      const doc = iframeRef.current.contentDocument;
      Object.entries(designTokens).forEach(([token, value]) => {
        doc.documentElement.style.setProperty(token, value);
      });
    }
  }, [designTokens]);

  // Apply dark mode to the preview
  useEffect(() => {
    if (iframeRef.current?.contentDocument) {
      const doc = iframeRef.current.contentDocument;
      if (isDarkMode) {
        doc.documentElement.classList.add('dark');
      } else {
        doc.documentElement.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      case 'desktop':
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const dimensions = getPreviewDimensions();

  if (!component) {
    return (
      <div className="h-full flex items-center justify-center bg-muted/20">
        <div className="text-center space-y-2">
          <div className="text-muted-foreground text-lg">No Component Selected</div>
          <p className="text-sm text-muted-foreground">
            Choose a component from the left panel to start editing
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-muted/20 p-4 overflow-auto">
      <div className="h-full flex items-center justify-center">
        <div
          className={cn(
            "bg-background border rounded-lg shadow-lg transition-all duration-300",
            previewMode !== 'desktop' && "mx-auto"
          )}
          style={{
            width: dimensions.width,
            height: dimensions.height,
            minHeight: previewMode === 'desktop' ? '400px' : dimensions.height,
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        >
          <div className="h-full overflow-auto">
            <PreviewContent
              component={component}
              props={props}
              designTokens={designTokens}
              isDarkMode={isDarkMode}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

interface PreviewContentProps {
  component: EditableComponent;
  props: Record<string, any>;
  designTokens: Record<string, string>;
  isDarkMode: boolean;
}

function PreviewContent({
  component,
  props,
  designTokens,
  isDarkMode,
}: PreviewContentProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // Apply design tokens to the container
  useEffect(() => {
    if (containerRef.current) {
      Object.entries(designTokens).forEach(([token, value]) => {
        containerRef.current!.style.setProperty(token, value);
      });
    }
  }, [designTokens]);

  // Apply dark mode class
  useEffect(() => {
    if (containerRef.current) {
      if (isDarkMode) {
        containerRef.current.classList.add('dark');
      } else {
        containerRef.current.classList.remove('dark');
      }
    }
  }, [isDarkMode]);

  try {
    const ComponentToRender = component.component;
    
    return (
      <div
        ref={containerRef}
        className={cn(
          "h-full w-full p-4",
          isDarkMode && "dark"
        )}
        style={{
          // Apply design tokens as inline styles for immediate effect
          ...Object.fromEntries(
            Object.entries(designTokens).map(([token, value]) => [token, value])
          ),
        }}
      >
        <div className="h-full flex items-center justify-center">
          <ComponentToRender {...props} />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering component:', error);
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <div className="text-destructive text-lg">Render Error</div>
          <p className="text-sm text-muted-foreground">
            Failed to render component with current props
          </p>
          <pre className="text-xs bg-muted p-2 rounded max-w-md overflow-auto">
            {error instanceof Error ? error.message : 'Unknown error'}
          </pre>
        </div>
      </div>
    );
  }
}
