"use client";

import { useState, useCallback, useEffect } from "react";
import type { VisualEditorState, VisualEditorConfig, EditableComponent } from "../types";

export function useVisualEditor(config: VisualEditorConfig) {
  const [state, setState] = useState<VisualEditorState>({
    selectedComponent: null,
    componentProps: {},
    designTokens: {},
    previewMode: 'desktop',
    isDarkMode: false,
    isCodeViewOpen: false,
  });

  // Initialize design tokens from CSS
  useEffect(() => {
    const initialTokens: Record<string, string> = {};
    config.designTokens.forEach(token => {
      const value = getComputedStyle(document.documentElement)
        .getPropertyValue(token.cssVar)
        .trim();
      if (value) {
        initialTokens[token.cssVar] = value;
      }
    });
    setState(prev => ({ ...prev, designTokens: initialTokens }));
  }, [config.designTokens]);

  const selectComponent = useCallback((component: EditableComponent) => {
    setState(prev => ({
      ...prev,
      selectedComponent: component,
      componentProps: { ...component.defaultProps },
    }));
  }, []);

  const updateComponentProp = useCallback((propName: string, value: any) => {
    setState(prev => ({
      ...prev,
      componentProps: {
        ...prev.componentProps,
        [propName]: value,
      },
    }));
  }, []);

  const updateDesignToken = useCallback((tokenVar: string, value: string) => {
    setState(prev => ({
      ...prev,
      designTokens: {
        ...prev.designTokens,
        [tokenVar]: value,
      },
    }));

    // Apply the token to the document
    document.documentElement.style.setProperty(tokenVar, value);
  }, []);

  const setPreviewMode = useCallback((mode: 'desktop' | 'tablet' | 'mobile') => {
    setState(prev => ({ ...prev, previewMode: mode }));
  }, []);

  const toggleDarkMode = useCallback(() => {
    setState(prev => {
      const newDarkMode = !prev.isDarkMode;
      // Toggle dark class on document
      if (newDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
      return { ...prev, isDarkMode: newDarkMode };
    });
  }, []);

  const toggleCodeView = useCallback(() => {
    setState(prev => ({ ...prev, isCodeViewOpen: !prev.isCodeViewOpen }));
  }, []);

  const resetComponent = useCallback(() => {
    if (state.selectedComponent) {
      setState(prev => ({
        ...prev,
        componentProps: { ...state.selectedComponent!.defaultProps },
      }));
    }
  }, [state.selectedComponent]);

  const exportCode = useCallback(() => {
    if (!state.selectedComponent) return null;

    const { selectedComponent, componentProps, designTokens } = state;
    
    // Generate component JSX
    const propsString = Object.entries(componentProps)
      .map(([key, value]) => {
        if (typeof value === 'string') {
          return `${key}="${value}"`;
        }
        if (typeof value === 'boolean') {
          return value ? key : '';
        }
        return `${key}={${JSON.stringify(value)}}`;
      })
      .filter(Boolean)
      .join('\n  ');

    const componentCode = `<${selectedComponent.name}${propsString ? `\n  ${propsString}` : ''} />`;

    // Generate CSS tokens
    const tokenCode = Object.entries(designTokens)
      .map(([token, value]) => `  ${token}: ${value};`)
      .join('\n');

    return {
      component: componentCode,
      tokens: tokenCode,
      imports: `import { ${selectedComponent.name} } from "@/components/${selectedComponent.name.toLowerCase()}";`,
    };
  }, [state]);

  return {
    state,
    actions: {
      selectComponent,
      updateComponentProp,
      updateDesignToken,
      setPreviewMode,
      toggleDarkMode,
      toggleCodeView,
      resetComponent,
      exportCode,
    },
  };
}
