"use client"

import { DashboardButton } from "./dashboard-button"
import { users, invitedUsers, roles } from "@/lib/dashboard-data"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserPlus, MoreHorizontal } from "lucide-react"

export function DashboardSettingsUsersPage() {
  return (
    <div className="space-y-8">
      {/* Existing Users */}
      <section aria-labelledby="existing-users">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h3
              id="existing-users"
              className="font-semibold text-gray-900 dark:text-gray-50"
            >
              Users
            </h3>
            <p className="text-sm leading-6 text-gray-500">
              Workspace administrators can add, manage, and remove users.
            </p>
          </div>
          <DashboardButton className="mt-4 w-full gap-2 sm:mt-0 sm:w-fit">
            <UserPlus className="size-4 shrink-0" />
            Add user
          </DashboardButton>
        </div>
        
        <Card className="mt-6">
          <CardContent className="p-0">
            <ul className="divide-y divide-gray-200 dark:divide-gray-800">
              {users.map((user) => (
                <li
                  key={user.name}
                  className="flex items-center justify-between gap-x-6 p-4"
                >
                  <div className="flex items-center gap-x-4 truncate">
                    <span
                      className="hidden size-9 shrink-0 items-center justify-center rounded-full border border-gray-300 bg-white text-xs text-gray-700 sm:flex dark:border-gray-800 dark:bg-gray-950 dark:text-gray-300"
                    >
                      {user.initials}
                    </span>
                    <div className="truncate">
                      <p className="truncate text-sm font-medium text-gray-900 dark:text-gray-50">
                        {user.name}
                      </p>
                      <p className="truncate text-xs text-gray-500">{user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select
                      defaultValue={user.role}
                      disabled={user.role === "admin"}
                    >
                      <SelectTrigger className="h-8 w-32">
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent align="end">
                        {roles.map((role) => (
                          <SelectItem
                            key={role.value}
                            value={role.value}
                            disabled={role.value === "admin"}
                          >
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <DashboardButton
                          variant="ghost"
                          className="size-8 p-0 hover:border hover:border-gray-300 hover:bg-gray-50 dark:hover:border-gray-700 dark:hover:bg-gray-900"
                        >
                          <MoreHorizontal className="size-4 shrink-0 text-gray-500" />
                        </DashboardButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-36">
                        <DropdownMenuItem disabled={user.role === "admin"}>
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600 dark:text-red-500"
                          disabled={user.role === "admin"}
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </section>

      {/* Pending Invitations */}
      <section aria-labelledby="pending-invitations">
        <h2
          id="pending-invitations"
          className="font-semibold text-gray-900 dark:text-gray-50"
        >
          Pending invitations
        </h2>
        
        <Card className="mt-6">
          <CardContent className="p-0">
            <ul className="divide-y divide-gray-200 dark:divide-gray-800">
              {invitedUsers.map((user) => (
                <li
                  key={user.initials}
                  className="flex items-center justify-between gap-x-6 p-4"
                >
                  <div className="flex items-center gap-x-4">
                    <span
                      className="hidden size-9 shrink-0 items-center justify-center rounded-full border border-dashed border-gray-300 bg-white text-xs text-gray-700 sm:flex dark:border-gray-800 dark:bg-gray-950 dark:text-gray-300"
                    >
                      {user.initials}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-50">
                        {user.email}
                      </p>
                      <p className="text-xs text-gray-500">
                        Expires in {user.expires} days
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Select defaultValue={user.role}>
                      <SelectTrigger className="h-8 w-32">
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent align="end">
                        {roles.map((role) => (
                          <SelectItem
                            key={role.value}
                            value={role.value}
                            disabled={role.value === "admin"}
                          >
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <DashboardButton
                          variant="ghost"
                          className="size-8 p-0 hover:border hover:border-gray-300 hover:bg-gray-50 dark:hover:border-gray-700 dark:hover:bg-gray-900"
                        >
                          <MoreHorizontal className="size-4 shrink-0 text-gray-500" />
                        </DashboardButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-36">
                        <DropdownMenuItem className="text-red-600 dark:text-red-500">
                          Revoke invitation
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
