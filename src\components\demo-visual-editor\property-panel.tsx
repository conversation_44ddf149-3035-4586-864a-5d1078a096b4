"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>lider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { RotateCcw } from "lucide-react";
import type { EditableComponentConfig, ComponentProperty } from "./types";

interface PropertyPanelProps {
  config: EditableComponentConfig;
  values: Record<string, any>;
  onChange: (propName: string, value: any) => void;
}

export function PropertyPanel({ config, values, onChange }: PropertyPanelProps) {
  const handleReset = () => {
    Object.entries(config.defaultProps).forEach(([key, value]) => {
      onChange(key, value);
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Properties</h3>
        <Button variant="outline" size="sm" onClick={handleReset}>
          <RotateCcw className="h-4 w-4 mr-1" />
          Reset
        </Button>
      </div>

      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="space-y-4">
          {config.properties.map((property) => (
            <PropertyControl
              key={property.name}
              property={property}
              value={values[property.name] ?? property.value}
              onChange={(value) => onChange(property.name, value)}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface PropertyControlProps {
  property: ComponentProperty;
  value: any;
  onChange: (value: any) => void;
}

function PropertyControl({ property, value, onChange }: PropertyControlProps) {
  const renderControl = () => {
    switch (property.type) {
      case 'text':
        return (
          <Input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${property.label.toLowerCase()}...`}
          />
        );

      case 'color':
        return (
          <div className="flex items-center space-x-2">
            <div
              className="w-8 h-8 rounded border flex-shrink-0"
              style={{ backgroundColor: value || '#000000' }}
            />
            <Input
              type="color"
              value={value || '#000000'}
              onChange={(e) => onChange(e.target.value)}
              className="w-16 h-8 p-0 border-0"
            />
            <Input
              type="text"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder="#000000"
              className="font-mono text-sm"
            />
          </div>
        );

      case 'number':
        const min = property.min ?? 0;
        const max = property.max ?? 100;
        const step = property.step ?? 1;
        
        return (
          <div className="space-y-2">
            <Slider
              value={[typeof value === 'number' ? value : 0]}
              onValueChange={([newValue]) => onChange(newValue)}
              min={min}
              max={max}
              step={step}
              className="w-full"
            />
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{min}</span>
              <span className="font-mono">{value}</span>
              <span>{max}</span>
            </div>
          </div>
        );

      case 'select':
        return (
          <Select value={value?.toString()} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${property.label.toLowerCase()}...`} />
            </SelectTrigger>
            <SelectContent>
              {property.options?.map((option) => (
                <SelectItem key={option.toString()} value={option.toString()}>
                  {formatOptionLabel(option)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'boolean':
        return (
          <div className="flex items-center justify-between">
            <span className="text-sm">{property.label}</span>
            <Switch
              checked={Boolean(value)}
              onCheckedChange={onChange}
            />
          </div>
        );

      default:
        return (
          <Input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );
    }
  };

  if (property.type === 'boolean') {
    return (
      <div className="space-y-2">
        {renderControl()}
        {property.description && (
          <p className="text-xs text-muted-foreground">{property.description}</p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={property.name} className="text-sm font-medium">
        {property.label}
      </Label>
      {renderControl()}
      {property.description && (
        <p className="text-xs text-muted-foreground">{property.description}</p>
      )}
      <Separator className="my-4" />
    </div>
  );
}

function formatOptionLabel(option: string | number): string {
  if (typeof option === 'string') {
    return option
      .replace(/[-_]/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, l => l.toUpperCase());
  }
  return option.toString();
}
