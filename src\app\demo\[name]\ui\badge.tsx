"use client";

import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { EditableComponentWrapper } from "@/components/demo-visual-editor/editable-component-wrapper";

// Editable Badge Components
function EditableDefaultBadge() {
  return (
    <EditableComponentWrapper
      componentName="badge"
      initialProps={{ variant: "default", children: "Badge" }}
    >
      <Badge>Badge</Badge>
    </EditableComponentWrapper>
  );
}

function EditableSecondaryBadge() {
  return (
    <EditableComponentWrapper
      componentName="badge"
      initialProps={{ variant: "secondary", children: "Secondary" }}
    >
      <Badge variant="secondary">Secondary</Badge>
    </EditableComponentWrapper>
  );
}

function EditableOutlineBadge() {
  return (
    <EditableComponentWrapper
      componentName="badge"
      initialProps={{ variant: "outline", children: "Outline" }}
    >
      <Badge variant="outline">Outline</Badge>
    </EditableComponentWrapper>
  );
}

function EditableDestructiveBadge() {
  return (
    <EditableComponentWrapper
      componentName="badge"
      initialProps={{ variant: "destructive", children: "Destructive" }}
    >
      <Badge variant="destructive">Destructive</Badge>
    </EditableComponentWrapper>
  );
}

export const badge = {
  name: "badge",
  components: {
    Default: <EditableDefaultBadge />,
    Secondary: <EditableSecondaryBadge />,
    Outline: <EditableOutlineBadge />,
    Destructive: <EditableDestructiveBadge />,

    DefaultLink: (
      <Badge>
        <Link href="#" className="flex items-center gap-1">
          Link
          <ArrowRight className="size-3" />
        </Link>
      </Badge>
    ),
    SecondaryLink: (
      <Badge variant="secondary">
        <Link href="#" className="flex items-center gap-1">
          Link
          <ArrowRight className="size-3" />
        </Link>
      </Badge>
    ),
    OutlineLink: (
      <Badge variant="outline">
        <Link href="#" className="flex items-center gap-1">
          Link
          <ArrowRight className="size-3" />
        </Link>
      </Badge>
    ),
    DestructiveLink: (
      <Badge variant="destructive">
        <Link href="#" className="flex items-center gap-1">
          Link
          <ArrowRight className="size-3" />
        </Link>
      </Badge>
    ),
  },
};
