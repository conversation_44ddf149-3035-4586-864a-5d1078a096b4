"use client";

import { Hero } from "@/components/hero";
import { EditableComponentWrapper } from "@/components/demo-visual-editor/editable-component-wrapper";

function EditableHero() {
  const initialProps = {
    title: "Build a Registry",
    description: "This starter helps you create a registry so you can distribute your custom components, hooks, pages, and other files to any React project",
    buttonText: "Learn more",
    buttonLink: "#sale",
    backgroundImage: "/assets/hero.png",
  };

  return (
    <EditableComponentWrapper
      componentName="hero"
      initialProps={initialProps}
    >
      <Hero {...initialProps} />
    </EditableComponentWrapper>
  );
}

export const hero = {
  name: "hero",
  components: {
    Default: <EditableHero />,
  },
};
