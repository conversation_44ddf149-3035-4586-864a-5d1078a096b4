"use client"

import { DashboardBadge } from "./dashboard-badge"
import { cx, formatters, percentageFormatter } from "@/lib/dashboard-utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export type ChartCardProps = {
  title: string
  value: number
  previousValue?: number
  type: "currency" | "unit"
  className?: string
}

export const getBadgeType = (value: number) => {
  if (value > 0) {
    return "success"
  } else if (value < 0) {
    if (value < -50) {
      return "warning"
    }
    return "error"
  } else {
    return "neutral"
  }
}

export function DashboardChartCard({
  title,
  value,
  previousValue,
  type,
  className,
}: ChartCardProps) {
  const formatter = formatters[type]
  const evolution = previousValue ? (value - previousValue) / previousValue : 0
  
  return (
    <Card className={cx("transition", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-900 dark:text-gray-50">
          {title}
        </CardTitle>
        {previousValue && (
          <DashboardBadge variant={getBadgeType(evolution)}>
            {percentageFormatter(evolution)}
          </DashboardBadge>
        )}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline justify-between">
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-50">
            {formatter(value)}
          </div>
          {previousValue && (
            <div className="text-sm text-gray-500">
              from {formatter(previousValue)}
            </div>
          )}
        </div>
        
        {/* Simple chart representation */}
        <div className="mt-4 h-20 w-full rounded bg-gradient-to-r from-indigo-500/20 to-indigo-600/20 p-4">
          <div className="flex h-full items-end justify-between gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <div
                key={i}
                className="w-full bg-indigo-500 rounded-t"
                style={{
                  height: `${Math.random() * 80 + 20}%`,
                }}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
